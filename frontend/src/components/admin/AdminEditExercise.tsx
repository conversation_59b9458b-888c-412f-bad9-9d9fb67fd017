import { useEffect, useState } from "react";
import { QuestionInput as QuestionInputType } from "../../types/exercise.types";
import { Button } from "../ui/Button";
import QuestionPreview from "./QuestionPreview";
import QuestionInput from "./QuestionInput";
import { useAdmin } from "../../context/AdminContext";
import TextInput from "../ui/input/TextInput";
import RichTextEditor from "../ui/input/RichTextEditor";
import { uploadImageWithFallback } from "../../utils/imageUpload";
import { useParams } from "react-router-dom";
import DraggableList from "../ui/DraggableList";
import { v4 as uuid } from "uuid";

const AdminEditExercise = () => {
  const { id: initialId } = useParams();

  const { fetchExerciseById, editExercise } = useAdmin();
  const [id, setId] = useState(initialId || "");
  const [name, setName] = useState("");
  const [description, setDescription] = useState("");
  const [questions, setQuestions] = useState<QuestionInputType[]>([]);

  const fetchExercise = async (id: string) => {
    const exercise = await fetchExerciseById(id);
    if (exercise) {
      setId(exercise.id);
      setName(exercise.name);
      setDescription(exercise.description);
      setQuestions(
        exercise.questions.map((q) => ({
          ...q,
          isEditing: true, // Set isEditing to true for all fetched questions
        }))
      );
    }
  };

  useEffect(() => {
    if (initialId) {
      fetchExercise(initialId);
    }
  }, []);

  const addQuestion = () => {
    const newQuestion: QuestionInputType = {
      id: uuid(),
      prompt: "",
      type: "free_text",
      required: false,
      isEditing: true,
    };
    setQuestions((prevQuestions) => [...prevQuestions, newQuestion]);
  };

  const addTextBlock = () => {
    const newTextBlock: QuestionInputType = {
      id: uuid(),
      type: "text_block",
      content: "",
      title: "",
      isEditing: true,
    } as any;
    setQuestions((prevQuestions) => [...prevQuestions, newTextBlock]);
  };

  const handleReorderQuestions = (reorderedQuestions: QuestionInputType[]) => {
    setQuestions(reorderedQuestions);
  };

  return (
    <div>
      <h1 className="text-2xl font-bold mb-4">Edit Exercise</h1>
      <div className="space-y-4">
        <div className="flex items-center space-x-2">
          <div>
            <TextInput
              label="ID"
              value={id}
              disabled
              placeholder="Exercise ID"
            />
          </div>
          <TextInput
            label="Name"
            value={name}
            onChange={setName}
            placeholder="Exercise Name"
          />
        </div>
        <div>
          <RichTextEditor
            key="exercise-edit-description-editor"
            label="Description"
            value={description}
            onChange={setDescription}
            placeholder="Enter exercise description with rich text formatting..."
            height="150px"
            enableImages={true}
            onImageUpload={uploadImageWithFallback}
          />
        </div>
        <h2 className="text-xl font-bold mt-4">Questions</h2>
        <DraggableList
          items={questions}
          onReorder={handleReorderQuestions}
          getItemId={(_, index) => `question-${index}`}
          renderItem={(question, index) => (
            <div>
              <p className="font-medium text-gray-700 mb-2">
                {question.type === "text_block"
                  ? `Text Block ${index + 1}`
                  : `Question ${index + 1}`}
                :
              </p>
              {question.isEditing ? (
                <QuestionInput
                  question={question}
                  onChange={(updatedQuestion: QuestionInputType) => {
                    const updatedQuestions = [...questions];
                    updatedQuestions[index] = updatedQuestion;
                    setQuestions(updatedQuestions);
                    console.log(questions);
                  }}
                  onDelete={() => {
                    const updatedQuestions = questions.filter(
                      (_, i) => i !== index
                    );
                    setQuestions(updatedQuestions);
                  }}
                />
              ) : (
                <QuestionPreview
                  question={question}
                  setIsEditing={() => {
                    const updatedQuestions = [...questions];
                    updatedQuestions[index].isEditing = true;
                    setQuestions(updatedQuestions);
                  }}
                />
              )}
            </div>
          )}
        />
        <div className="flex space-x-4">
          <Button onClick={addQuestion}>Add Question</Button>
          <Button onClick={addTextBlock} variant="outline">
            Add Text Block
          </Button>
          <Button
            onClick={() => {
              console.log("Creating exercise with data:", {
                id,
                name,
                description,
                questions,
              });
              editExercise(id, {
                name,
                description,
                questions: questions.map((q) => {
                  const { isEditing, ...rest } = q; // Exclude isEditing from the data sent to the backend
                  return rest;
                }),
              })
                .then(() => {
                  // Reset form after successful creation
                  setId("");
                  setName("");
                  setDescription("");
                  setQuestions([]);
                })
                .catch((error) => {
                  console.error("Error updating exercise:", error);
                });
            }}
          >
            Update Exercise
          </Button>
        </div>
      </div>
    </div>
  );
};

export default AdminEditExercise;
