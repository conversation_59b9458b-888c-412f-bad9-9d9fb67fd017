import { useEffect } from "react";
import { useNavigate } from "react-router-dom";
import { useAdmin } from "../../context/AdminContext";
import { PageHeader } from "../ui/PageHeader";
import { Card, CardHeader } from "../ui/Card";
import { LoadingSpinner } from "../ui/LoadingSpinner";
import { ErrorMessage } from "../ui/ErrorMessage";
import MetricCard from "./MetricCard";
import QuickActionCard from "./QuickActionCard";
import ActivityFeed from "./ActivityFeed";
import {
  Users,
  Building2,
  BookOpen,
  Eye,
  ClipboardCheck,
  TrendingUp,
  UserPlus,
  ClipboardPlus,
  BarChart3,
} from "lucide-react";

const AdminHome = () => {
  const { dashboardStats, loading, error, fetchDashboardStats, clearError } =
    useAdmin();
  const navigate = useNavigate();

  useEffect(() => {
    fetchDashboardStats();
  }, [fetchDashboardStats]);

  if (loading.dashboard) {
    return <LoadingSpinner />;
  }

  return (
    <div className="space-y-6">
      <PageHeader
        title="Admin Dashboard"
        description="System overview and management"
      />

      {error && <ErrorMessage message={error} onDismiss={clearError} />}

      {/* Key Metrics */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        <MetricCard
          title="Total Users"
          value={dashboardStats?.totalUsers || 0}
          change="+12%"
          trend="up"
          icon={Users}
          description="All system users"
          loading={loading.dashboard}
        />
        <MetricCard
          title="Organizations"
          value={dashboardStats?.totalOrganizations || 0}
          change="+5%"
          trend="up"
          icon={Building2}
          description="Active organizations"
          loading={loading.dashboard}
        />
        <MetricCard
          title="Exercises"
          value={dashboardStats?.totalExercises || 0}
          change="+8%"
          trend="up"
          icon={BookOpen}
          description="Available exercises"
          loading={loading.dashboard}
        />
        <MetricCard
          title="Imagery Exercises"
          value={dashboardStats?.totalVisualizations || 0}
          change="+3%"
          trend="up"
          icon={Eye}
          description="Imagery exercise templates"
          loading={loading.dashboard}
        />
      </div>

      {/* Activity Metrics */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        <MetricCard
          title="Exercise Assignments"
          value={dashboardStats?.activityStats?.exerciseAssignments || 0}
          change="+23%"
          trend="up"
          icon={ClipboardCheck}
          description="Total assigned"
          loading={loading.dashboard}
        />
        <MetricCard
          title="Exercise Completions"
          value={dashboardStats?.activityStats?.exerciseCompletions || 0}
          change="+15%"
          trend="up"
          icon={TrendingUp}
          description="Total completed"
          loading={loading.dashboard}
        />
        <MetricCard
          title="Imagery Exercise Assignments"
          value={dashboardStats?.activityStats?.visualizationAssignments || 0}
          change="+8%"
          trend="up"
          icon={Eye}
          description="Total assigned"
          loading={loading.dashboard}
        />
        <MetricCard
          title="Completion Rate"
          value={`${dashboardStats?.activityStats?.completionRate || 0}%`}
          change="+5%"
          trend="up"
          icon={BarChart3}
          description="Exercise completion"
          loading={loading.dashboard}
        />
      </div>

      {/* User Distribution */}
      {dashboardStats?.usersByRole && (
        <Card>
          <CardHeader title="User Distribution" />
          <div className="p-6 pt-2">
            <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
              <div className="text-center p-4 bg-purple-50 rounded-lg">
                <p className="text-2xl font-bold text-purple-600">
                  {dashboardStats.usersByRole.admin}
                </p>
                <p className="text-sm text-purple-700">Admins</p>
              </div>
              <div className="text-center p-4 bg-blue-50 rounded-lg">
                <p className="text-2xl font-bold text-blue-600">
                  {dashboardStats.usersByRole.hrAdmin}
                </p>
                <p className="text-sm text-blue-700">HR Admins</p>
              </div>
              <div className="text-center p-4 bg-green-50 rounded-lg">
                <p className="text-2xl font-bold text-green-600">
                  {dashboardStats.usersByRole.coach}
                </p>
                <p className="text-sm text-green-700">Coaches</p>
              </div>
              <div className="text-center p-4 bg-orange-50 rounded-lg">
                <p className="text-2xl font-bold text-orange-600">
                  {dashboardStats.usersByRole.coachee}
                </p>
                <p className="text-sm text-orange-700">Coachees</p>
              </div>
            </div>
          </div>
        </Card>
      )}

      {/* Quick Actions */}
      <Card>
        <CardHeader title="Quick Actions" />
        <div className="p-6 pt-2">
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <QuickActionCard
              title="Create User"
              description="Add a new user to the system"
              icon={UserPlus}
              href="/dashboard/users/create"
            />
            <QuickActionCard
              title="New Organization"
              description="Set up a new organization"
              icon={Building2}
              href="/dashboard/organizations/create"
            />
            <QuickActionCard
              title="Create Exercise"
              description="Design a new exercise"
              icon={ClipboardPlus}
              href="/dashboard/exercises/create"
            />
          </div>
        </div>
      </Card>

      {/* Recent Activity */}
      <Card>
        <CardHeader title="Recent Activity" />
        <div className="p-6 pt-2">
          <ActivityFeed
            activities={dashboardStats?.recentActivity || []}
            loading={loading.dashboard}
          />
        </div>
      </Card>

      {/* System Overview */}
      <Card>
        <CardHeader title="System Overview" />
        <div className="p-6 pt-2">
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div className="text-center p-4 border border-gray-200 rounded-lg">
              <BarChart3 className="h-8 w-8 text-blue-600 mx-auto mb-2" />
              <h3 className="text-lg font-medium text-gray-900">Analytics</h3>
              <p className="text-sm text-gray-500">
                View system analytics and reports
              </p>
              <button
                onClick={() => navigate("/dashboard/analytics")}
                className="mt-2 text-blue-600 hover:text-blue-800 text-sm font-medium"
              >
                View Analytics →
              </button>
            </div>

            <div className="text-center p-4 border border-gray-200 rounded-lg">
              <Users className="h-8 w-8 text-green-600 mx-auto mb-2" />
              <h3 className="text-lg font-medium text-gray-900">
                User Management
              </h3>
              <p className="text-sm text-gray-500">Manage all system users</p>
              <button
                onClick={() => navigate("/dashboard/users")}
                className="mt-2 text-green-600 hover:text-green-800 text-sm font-medium"
              >
                Manage Users →
              </button>
            </div>

            <div className="text-center p-4 border border-gray-200 rounded-lg">
              <BookOpen className="h-8 w-8 text-purple-600 mx-auto mb-2" />
              <h3 className="text-lg font-medium text-gray-900">Content</h3>
              <p className="text-sm text-gray-500">
                Manage exercises and content
              </p>
              <button
                onClick={() => navigate("/dashboard/exercises")}
                className="mt-2 text-purple-600 hover:text-purple-800 text-sm font-medium"
              >
                Manage Content →
              </button>
            </div>
          </div>
        </div>
      </Card>
    </div>
  );
};

export default AdminHome;
