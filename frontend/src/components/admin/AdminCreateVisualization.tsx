import { useState, useEffect } from "react";
import { useNavigate } from "react-router-dom";
import { PageHeader } from "../ui/PageHeader";
import { Card, CardHeader } from "../ui/Card";
import { Button } from "../ui/Button";

import { createVisualization } from "../../api/visualizations";
import {
  generateVisualizationAudio,
  getAvailableVoices,
  TTSOptions,
} from "../../api/audio";
import { ErrorMessage } from "../ui/ErrorMessage";
import { LoadingSpinner } from "../ui/LoadingSpinner";
import { Volume2 } from "lucide-react";
import { AudioPlayer } from "../ui/AudioPlayer";

const AdminCreateVisualization = () => {
  const navigate = useNavigate();
  const [id, setId] = useState("");
  const [title, setTitle] = useState("");
  const [description, setDescription] = useState("");
  const [audioUrl, setAudioUrl] = useState("");
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  // Audio generation state
  const [generateAudio, setGenerateAudio] = useState(true);
  const [audioGenerating, setAudioGenerating] = useState(false);
  const [audioGenerated, setAudioGenerated] = useState(false);
  const [availableVoices, setAvailableVoices] = useState<string[]>([]);
  const [selectedVoice, setSelectedVoice] = useState("af_bella");
  const [selectedSpeed, setSelectedSpeed] = useState(1.0);
  const [selectedLanguage, setSelectedLanguage] = useState("en");
  const [supportedLanguages, setSupportedLanguages] = useState<string[]>([]);

  // Load available voices on component mount
  useEffect(() => {
    const loadVoices = async () => {
      try {
        const voicesData = await getAvailableVoices();
        setAvailableVoices(voicesData.voices);
        setSupportedLanguages(voicesData.supportedLanguages);
        setSelectedVoice(voicesData.defaultVoice);
      } catch (error) {
        console.error("Error loading voices:", error);
      }
    };

    loadVoices();
  }, []);

  const handleCreateVisualization = async () => {
    if (!title.trim()) {
      setError("Title is required");
      return;
    }

    if (!description.trim()) {
      setError("Description is required");
      return;
    }

    setLoading(true);
    setError(null);

    try {
      // Step 1: Create the visualization first
      const visualizationData = {
        id: id.trim() || undefined,
        title: title.trim(),
        description: description.trim(),
        audioUrl: audioUrl.trim() || undefined,
      };

      const createdVisualization = await createVisualization(visualizationData);

      // Step 2: Generate audio if enabled and description exists
      if (generateAudio && description.trim()) {
        try {
          setAudioGenerating(true);

          const ttsOptions: TTSOptions = {
            voice: selectedVoice,
            speed: selectedSpeed,
            language: selectedLanguage,
          };

          const audioResponse = await generateVisualizationAudio(
            createdVisualization.id,
            ttsOptions
          );

          setAudioUrl(audioResponse.audioUrl);
          setAudioGenerated(true);

          console.log("Audio generated successfully:", audioResponse.audioUrl);
        } catch (audioError) {
          console.error("Error generating audio:", audioError);
          // Don't fail the entire creation if audio generation fails
          setError(
            "Imagery exercise created successfully, but audio generation failed. You can generate audio later from the edit page."
          );
        } finally {
          setAudioGenerating(false);
        }
      }

      // Reset form after successful creation
      setId("");
      setTitle("");
      setDescription("");
      setAudioUrl("");
      setAudioGenerated(false);

      // Navigate back to visualizations list
      navigate("/dashboard/visualizations");
    } catch (error) {
      console.error("Error creating imagery exercise:", error);
      setError("Failed to create imagery exercise. Please try again.");
    } finally {
      setLoading(false);
    }
  };

  const handleCancel = () => {
    navigate("/dashboard/visualizations");
  };

  if (loading) {
    return <LoadingSpinner />;
  }

  return (
    <div className="space-y-6">
      <PageHeader
        title="Create Imagery Exercise"
        description="Create a new imagery exercise"
      />

      {error && <ErrorMessage message={error} />}

      <Card>
        <CardHeader title="Imagery Exercise Details" />
        <div className="p-6 pt-0 space-y-4">
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              ID (Optional)
            </label>
            <input
              type="text"
              value={id}
              onChange={(e) => setId(e.target.value)}
              placeholder="Leave empty for auto-generated ID"
              className="w-full border border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500 px-2 py-1.5"
            />
            <p className="text-xs text-gray-500 mt-1">
              If not provided, an ID will be automatically generated
            </p>
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              Title *
            </label>
            <input
              type="text"
              value={title}
              onChange={(e) => setTitle(e.target.value)}
              placeholder="Enter visualization title"
              className="w-full border border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500 px-2 py-1.5"
              required
            />
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              Description *
            </label>
            <textarea
              value={description}
              onChange={(e) => setDescription(e.target.value)}
              placeholder="Enter visualization description or transcript"
              className="w-full border border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500 px-2 py-1.5"
              rows={6}
              required
            />
          </div>

          {/* Audio Generation Section */}
          <div className="border-t pt-4">
            <div className="flex items-center justify-between mb-4">
              <h3 className="text-lg font-medium text-gray-900">
                Audio Generation
              </h3>
              <div className="flex items-center">
                <input
                  type="checkbox"
                  id="generateAudio"
                  checked={generateAudio}
                  onChange={(e) => setGenerateAudio(e.target.checked)}
                  className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                />
                <label
                  htmlFor="generateAudio"
                  className="ml-2 text-sm text-gray-700"
                >
                  Auto-generate audio from description
                </label>
              </div>
            </div>

            {generateAudio && (
              <div className="space-y-4 bg-gray-50 p-4 rounded-lg">
                <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      Voice
                    </label>
                    <select
                      value={selectedVoice}
                      onChange={(e) => setSelectedVoice(e.target.value)}
                      className="w-full border border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500 px-2 py-1.5"
                    >
                      {availableVoices.map((voice) => (
                        <option key={voice} value={voice}>
                          {voice
                            .replace(/_/g, " ")
                            .replace(/\b\w/g, (l) => l.toUpperCase())}
                        </option>
                      ))}
                    </select>
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      Language
                    </label>
                    <select
                      value={selectedLanguage}
                      onChange={(e) => setSelectedLanguage(e.target.value)}
                      className="w-full border border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500 px-2 py-1.5"
                    >
                      {supportedLanguages.map((lang) => (
                        <option key={lang} value={lang}>
                          {lang.toUpperCase()}
                        </option>
                      ))}
                    </select>
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      Speed: {selectedSpeed}x
                    </label>
                    <input
                      type="range"
                      min="0.5"
                      max="2.0"
                      step="0.1"
                      value={selectedSpeed}
                      onChange={(e) =>
                        setSelectedSpeed(parseFloat(e.target.value))
                      }
                      className="w-full h-2 bg-gray-200 rounded-lg appearance-none cursor-pointer"
                    />
                    <div className="flex justify-between text-xs text-gray-500 mt-1">
                      <span>0.5x</span>
                      <span>2.0x</span>
                    </div>
                  </div>
                </div>

                {audioGenerating && (
                  <div className="flex items-center space-x-2 text-blue-600">
                    <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-blue-600"></div>
                    <span className="text-sm">Generating audio...</span>
                  </div>
                )}

                {audioGenerated && audioUrl && (
                  <div className="space-y-3">
                    <div className="flex items-center space-x-2 p-3 bg-green-50 border border-green-200 rounded-lg">
                      <Volume2 className="h-5 w-5 text-green-600" />
                      <span className="text-sm text-green-800 font-medium">
                        Audio generated successfully!
                      </span>
                    </div>
                    <AudioPlayer
                      audioUrl={audioUrl}
                      title={title || "New Visualization Audio"}
                      onError={(error) => setError(error)}
                    />
                  </div>
                )}

                <p className="text-xs text-gray-500">
                  Audio will be automatically generated from the description
                  text using the selected voice and settings.
                </p>
              </div>
            )}

            {!generateAudio && (
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Audio URL (Optional)
                </label>
                <input
                  type="url"
                  value={audioUrl}
                  onChange={(e) => setAudioUrl(e.target.value)}
                  placeholder="https://example.com/audio.mp3"
                  className="w-full border border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500 px-2 py-1.5"
                />
                <p className="text-xs text-gray-500 mt-1">
                  Manually enter the URL to an existing audio file
                </p>
              </div>
            )}
          </div>

          <div className="flex space-x-4 pt-4">
            <Button
              onClick={handleCreateVisualization}
              disabled={loading || audioGenerating}
            >
              {loading
                ? audioGenerating
                  ? "Creating & Generating Audio..."
                  : "Creating..."
                : "Create Imagery Exercise"}
            </Button>
            <Button variant="outline" onClick={handleCancel}>
              Cancel
            </Button>
          </div>
        </div>
      </Card>
    </div>
  );
};

export default AdminCreateVisualization;
