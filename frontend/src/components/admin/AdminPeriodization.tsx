import React, { useState, useEffect } from "react";
import { Link } from "react-router-dom";
import { PageHeader } from "../ui/PageHeader";
import { Card, CardHeader } from "../ui/Card";
import { Button } from "../ui/Button";
import { DataTable } from "../ui/DataTable";
import { LoadingSpinner } from "../ui/LoadingSpinner";
import { ErrorMessage } from "../ui/ErrorMessage";
import { Column } from "../../types/components.types";
import { useAdmin } from "../../context/AdminContext";
import {
  Calendar,
  Play,
  BookOpen,
  Eye,
  BicepsFlexed,
  HeartPlus,
} from "lucide-react";
import {
  generatePeriodization,
  PeriodizationEntry,
} from "../../utils/periodization";
import { Label } from "../ui/Label";
import { InteractivePeriodizationBar } from "../ui/InteractivePeriodizationBar";
import { PeriodBreakdownBar } from "../ui/PeriodBreakdownBar";
import { DateRangeBar } from "../ui/DateRangeBar";

const AdminPeriodization = () => {
  const {
    allExercises,
    allVisualizations,
    loading,
    error,
    clearError,
    fetchAllExercises,
    fetchAllVisualizations,
  } = useAdmin();
  // Five periodization dates with defaults
  const getDefaultDates = () => {
    const today = new Date();
    const offSeasonStart = new Date(today);
    offSeasonStart.setDate(today.getDate() + 7); // Start next week

    const prepStart = new Date(offSeasonStart);
    prepStart.setDate(offSeasonStart.getDate() + 12 * 7); // 12 weeks later

    const preCompStart = new Date(prepStart);
    preCompStart.setDate(prepStart.getDate() + 12 * 7); // 12 weeks later

    const competitionStart = new Date(preCompStart);
    competitionStart.setDate(preCompStart.getDate() + 4 * 7); // 4 weeks later

    const competitionEnd = new Date(competitionStart);
    competitionEnd.setDate(competitionStart.getDate() + 2 * 7); // 2 weeks later

    return {
      offSeasonStart: offSeasonStart.toISOString().split("T")[0],
      prepStart: prepStart.toISOString().split("T")[0],
      preCompStart: preCompStart.toISOString().split("T")[0],
      competitionStart: competitionStart.toISOString().split("T")[0],
      competitionEnd: competitionEnd.toISOString().split("T")[0],
    };
  };

  const defaultDates = getDefaultDates();
  const [offSeasonStartDate, setOffSeasonStartDate] = useState<string>(
    defaultDates.offSeasonStart
  );
  const [prepStartDate, setPrepStartDate] = useState<string>(
    defaultDates.prepStart
  );
  const [preCompStartDate, setPreCompStartDate] = useState<string>(
    defaultDates.preCompStart
  );
  const [competitionStartDate, setCompetitionStartDate] = useState<string>(
    defaultDates.competitionStart
  );
  const [competitionEndDate, setCompetitionEndDate] = useState<string>(
    defaultDates.competitionEnd
  );

  // Calculated phase durations from the draggable bar
  const [phaseDurations, setPhaseDurations] = useState({
    offSeasonWeeks: 0,
    prepWeeks: 0,
    preCompWeeks: 0,
    competitionWeeks: 0,
  });

  // Mental wellness date range (in weeks from start)
  const [mentalWellnessRange, setMentalWellnessRange] = useState({
    startWeek: 1,
    endWeek: 1,
  });

  // Scheduling options
  const [scheduleMentalToughness, setScheduleMentalToughness] =
    useState<boolean>(true);
  const [scheduleMentalWellness, setScheduleMentalWellness] =
    useState<boolean>(true);

  const [periodizationData, setPeriodizationData] = useState<
    PeriodizationEntry[]
  >([]);
  const [errors, setErrors] = useState<string[]>([]);
  const [isGenerating, setIsGenerating] = useState(false);

  useEffect(() => {
    fetchAllExercises();
    fetchAllVisualizations();
  }, [fetchAllExercises, fetchAllVisualizations]);

  // Calculate total weeks from all dates
  const totalWeeks = React.useMemo(() => {
    if (!offSeasonStartDate || !competitionEndDate) return 0;
    const start = new Date(offSeasonStartDate);
    const end = new Date(competitionEndDate);
    return Math.ceil(
      (end.getTime() - start.getTime()) / (7 * 24 * 60 * 60 * 1000)
    );
  }, [offSeasonStartDate, competitionEndDate]);

  // Calculate period breakdown from the 5 dates
  const periodBreakdown = React.useMemo(() => {
    if (
      !offSeasonStartDate ||
      !prepStartDate ||
      !preCompStartDate ||
      !competitionStartDate ||
      !competitionEndDate
    ) {
      return null;
    }

    const dates = [
      new Date(offSeasonStartDate),
      new Date(prepStartDate),
      new Date(preCompStartDate),
      new Date(competitionStartDate),
      new Date(competitionEndDate),
    ];

    // Validate dates are in order
    for (let i = 1; i < dates.length; i++) {
      if (dates[i] <= dates[i - 1]) {
        return null; // Invalid date order
      }
    }

    const offSeasonWeeks = Math.ceil(
      (dates[1].getTime() - dates[0].getTime()) / (7 * 24 * 60 * 60 * 1000)
    );
    const prepWeeks = Math.ceil(
      (dates[2].getTime() - dates[1].getTime()) / (7 * 24 * 60 * 60 * 1000)
    );
    const preCompWeeks = Math.ceil(
      (dates[3].getTime() - dates[2].getTime()) / (7 * 24 * 60 * 60 * 1000)
    );
    const competitionWeeks = Math.ceil(
      (dates[4].getTime() - dates[3].getTime()) / (7 * 24 * 60 * 60 * 1000)
    );

    return {
      offSeasonWeeks,
      prepWeeks,
      preCompWeeks,
      competitionWeeks,
      totalWeeks: offSeasonWeeks + prepWeeks + preCompWeeks + competitionWeeks,
    };
  }, [
    offSeasonStartDate,
    prepStartDate,
    preCompStartDate,
    competitionStartDate,
    competitionEndDate,
  ]);

  // Initialize mental wellness range when total weeks change
  useEffect(() => {
    if (totalWeeks > 0) {
      setMentalWellnessRange({
        startWeek: 1,
        endWeek: totalWeeks,
      });
    }
  }, [totalWeeks]);

  // Calculate individual phase dates based on the phase durations from the interactive bar
  const calculatePhaseDates = () => {
    if (!offSeasonStartDate || !competitionEndDate) {
      return null;
    }

    const startDate = new Date(offSeasonStartDate);

    // Calculate end dates for each phase based on the interactive bar adjustments
    const offSeasonEndDate = new Date(startDate);
    offSeasonEndDate.setDate(
      startDate.getDate() + phaseDurations.offSeasonWeeks * 7 - 1
    );

    const prepStartDate = new Date(offSeasonEndDate);
    prepStartDate.setDate(offSeasonEndDate.getDate() + 1);
    const prepEndDate = new Date(prepStartDate);
    prepEndDate.setDate(
      prepStartDate.getDate() + phaseDurations.prepWeeks * 7 - 1
    );

    const preCompStartDate = new Date(prepEndDate);
    preCompStartDate.setDate(prepEndDate.getDate() + 1);
    const preCompEndDate = new Date(preCompStartDate);
    preCompEndDate.setDate(
      preCompStartDate.getDate() + phaseDurations.preCompWeeks * 7 - 1
    );

    const compStartDate = new Date(preCompEndDate);
    compStartDate.setDate(preCompEndDate.getDate() + 1);
    const compEndDate = new Date(competitionEndDate);

    // Calculate mental wellness dates
    const mentalWellnessStartDate = new Date(startDate);
    mentalWellnessStartDate.setDate(
      startDate.getDate() + (mentalWellnessRange.startWeek - 1) * 7
    );

    const mentalWellnessEndDate = new Date(startDate);
    mentalWellnessEndDate.setDate(
      startDate.getDate() + (mentalWellnessRange.endWeek - 1) * 7 + 6
    );

    return {
      offSeasonStart: startDate,
      offSeasonEnd: offSeasonEndDate,
      prepStart: prepStartDate,
      prepEnd: prepEndDate,
      preCompStart: preCompStartDate,
      preCompEnd: preCompEndDate,
      compStart: compStartDate,
      compEnd: compEndDate,
      mentalWellnessStart: mentalWellnessStartDate,
      mentalWellnessEnd: mentalWellnessEndDate,
    };
  };

  const handleGeneratePeriodization = async () => {
    if (
      !offSeasonStartDate ||
      !prepStartDate ||
      !preCompStartDate ||
      !competitionStartDate ||
      !competitionEndDate
    ) {
      return;
    }

    const phaseDates = calculatePhaseDates();
    if (!phaseDates) {
      return;
    }

    setIsGenerating(true);
    setErrors([]);

    const result = generatePeriodization(
      phaseDates.offSeasonStart,
      phaseDates.offSeasonEnd,
      phaseDates.prepStart,
      phaseDates.prepEnd,
      phaseDates.preCompStart,
      phaseDates.preCompEnd,
      phaseDates.compStart,
      phaseDates.compEnd,
      allExercises,
      scheduleMentalToughness,
      scheduleMentalWellness,
      phaseDates.mentalWellnessStart,
      phaseDates.mentalWellnessEnd
    );

    setPeriodizationData(result.entries);
    setErrors(result.errors);
    setIsGenerating(false);
  };

  const columns: Column<PeriodizationEntry>[] = [
    {
      header: "Week",
      accessorKey: "week",
      cell: (entry) => (
        <div className="flex items-center">
          <Calendar className="h-4 w-4 text-blue-600 mr-2" />
          <span className="font-medium">Week {entry.week}</span>
        </div>
      ),
    },
    {
      header: "Assignment Date",
      accessorKey: "assignmentDate",
      cell: (entry) => (
        <span className="text-sm text-gray-900">
          {new Date(entry.assignmentDate).toLocaleDateString("en-US", {
            weekday: "short",
            year: "numeric",
            month: "short",
            day: "numeric",
          })}
        </span>
      ),
    },
    {
      header: "Type",
      accessorKey: "exerciseType",
      cell: (entry) => (
        <div className="flex items-center">
          {entry.exerciseType === "MT exercise" ? (
            <Label icon={BicepsFlexed} color="orange" text="MT Exercise" />
          ) : entry.exerciseType === "MW exercise" ? (
            <Label icon={HeartPlus} color="green" text="MW Exercise" />
          ) : (
            <Label icon={Eye} color="purple" text="Imagery Exercise" />
          )}
        </div>
      ),
    },
    {
      header: "Exercise/Visualization",
      accessorKey: "exerciseName",
      cell: (entry) => (
        <div>
          <Link
            to={`/dashboard/exercises/${entry.exerciseId}`}
            target="_blank"
            className="text-sm font-medium text-blue-700 hover:underline cursor-pointer"
            title={`View details for ${entry.exerciseName}`}
            rel="noopener noreferrer"
          >
            {entry.exerciseName}
          </Link>
          <p className="text-xs text-gray-500">ID: {entry.exerciseId}</p>
        </div>
      ),
    },
  ];

  if (loading.exercises || loading.visualizations) {
    return <LoadingSpinner />;
  }

  return (
    <div className="space-y-6">
      <PageHeader
        title="Periodization Testing"
        description="Test the periodization algorithm by generating assignment schedules"
      />

      {error && <ErrorMessage message={error} onDismiss={clearError} />}

      {/* Configuration Form */}
      <Card>
        <CardHeader title="Periodization Configuration" />
        <div className="p-6 pt-0">
          {/* Season Dates */}
          <div className="space-y-6">
            <div>
              <h3 className="text-lg font-medium text-gray-900 mb-4">
                Periodization Dates
              </h3>
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 mb-6">
                <div>
                  <label
                    htmlFor="offSeasonStartDate"
                    className="block text-sm font-medium text-gray-700 mb-2"
                  >
                    Off Season Start Date
                  </label>
                  <input
                    type="date"
                    id="offSeasonStartDate"
                    value={offSeasonStartDate}
                    onChange={(e) => setOffSeasonStartDate(e.target.value)}
                    className="block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500 sm:text-sm"
                  />
                </div>

                <div>
                  <label
                    htmlFor="prepStartDate"
                    className="block text-sm font-medium text-gray-700 mb-2"
                  >
                    Prep Start Date
                  </label>
                  <input
                    type="date"
                    id="prepStartDate"
                    value={prepStartDate}
                    onChange={(e) => setPrepStartDate(e.target.value)}
                    className="block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500 sm:text-sm"
                  />
                </div>

                <div>
                  <label
                    htmlFor="preCompStartDate"
                    className="block text-sm font-medium text-gray-700 mb-2"
                  >
                    Pre-Comp Start Date
                  </label>
                  <input
                    type="date"
                    id="preCompStartDate"
                    value={preCompStartDate}
                    onChange={(e) => setPreCompStartDate(e.target.value)}
                    className="block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500 sm:text-sm"
                  />
                </div>

                <div>
                  <label
                    htmlFor="competitionStartDate"
                    className="block text-sm font-medium text-gray-700 mb-2"
                  >
                    Competition Start Date
                  </label>
                  <input
                    type="date"
                    id="competitionStartDate"
                    value={competitionStartDate}
                    onChange={(e) => setCompetitionStartDate(e.target.value)}
                    className="block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500 sm:text-sm"
                  />
                </div>

                <div>
                  <label
                    htmlFor="competitionEndDate"
                    className="block text-sm font-medium text-gray-700 mb-2"
                  >
                    Competition End Date
                  </label>
                  <input
                    type="date"
                    id="competitionEndDate"
                    value={competitionEndDate}
                    onChange={(e) => setCompetitionEndDate(e.target.value)}
                    className="block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500 sm:text-sm"
                  />
                </div>
              </div>

              {/* Period Breakdown Bar (Non-interactive) */}
              {periodBreakdown ? (
                <div>
                  <h4 className="text-md font-medium text-gray-900 mb-3">
                    Current Season Overview
                  </h4>
                  <p className="text-sm text-gray-600 mb-4">
                    This shows the breakdown of periods based on your selected
                    dates.
                  </p>
                  <PeriodBreakdownBar
                    offSeasonWeeks={periodBreakdown.offSeasonWeeks}
                    prepWeeks={periodBreakdown.prepWeeks}
                    preCompWeeks={periodBreakdown.preCompWeeks}
                    competitionWeeks={periodBreakdown.competitionWeeks}
                    totalWeeks={periodBreakdown.totalWeeks}
                  />
                </div>
              ) : (
                offSeasonStartDate &&
                prepStartDate &&
                preCompStartDate &&
                competitionStartDate &&
                competitionEndDate && (
                  <div className="p-4 bg-yellow-50 border border-yellow-200 rounded-md">
                    <h4 className="text-md font-medium text-yellow-800 mb-2">
                      Invalid Date Order
                    </h4>
                    <p className="text-sm text-yellow-700">
                      Please ensure dates are in chronological order: Off Season
                      Start → Prep Start → Pre-Comp Start → Competition Start →
                      Competition End
                    </p>
                  </div>
                )
              )}

              {/* Interactive Periodization Bar */}
              <div>
                <h4 className="text-md font-medium text-gray-900 mb-3">
                  Mental Toughness Skills Development Schedule
                </h4>
                <p className="text-sm text-gray-600 mb-4">
                  Drag the dividers to adjust when phases start and end within
                  the overall period. You can create gaps or overlaps as needed
                  for your periodization strategy.
                </p>
                <InteractivePeriodizationBar
                  totalWeeks={totalWeeks}
                  onPhasesChange={setPhaseDurations}
                />
              </div>

              {/* Mental Wellness Date Range */}
              <div>
                <h4 className="text-md font-medium text-gray-900 mb-3">
                  Mental Wellness Skills Development Schedule
                </h4>
                <p className="text-sm text-gray-600 mb-4">
                  Drag the start and end handles to specify when mental wellness
                  exercises should be scheduled.
                </p>
                <DateRangeBar
                  totalStartDate={offSeasonStartDate}
                  totalEndDate={competitionEndDate}
                  rangeStartWeek={mentalWellnessRange.startWeek}
                  rangeEndWeek={mentalWellnessRange.endWeek}
                  onRangeChange={(startWeek, endWeek) =>
                    setMentalWellnessRange({ startWeek, endWeek })
                  }
                  title="Mental Wellness Range"
                  color="bg-purple-500"
                />
              </div>
            </div>

            {/* Scheduling Options */}
            <div>
              <h3 className="text-lg font-medium text-gray-900 mb-4">
                Scheduling Options
              </h3>
              <div className="space-y-4">
                <div className="flex items-center">
                  <input
                    id="scheduleMentalToughness"
                    type="checkbox"
                    checked={scheduleMentalToughness}
                    onChange={(e) =>
                      setScheduleMentalToughness(e.target.checked)
                    }
                    className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                  />
                  <label
                    htmlFor="scheduleMentalToughness"
                    className="ml-2 block text-sm text-gray-900"
                  >
                    Schedule Mental Toughness Exercises
                  </label>
                </div>
                <div className="flex items-center">
                  <input
                    id="scheduleMentalWellness"
                    type="checkbox"
                    checked={scheduleMentalWellness}
                    onChange={(e) =>
                      setScheduleMentalWellness(e.target.checked)
                    }
                    className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                  />
                  <label
                    htmlFor="scheduleMentalWellness"
                    className="ml-2 block text-sm text-gray-900"
                  >
                    Schedule Mental Wellness Exercises
                  </label>
                </div>
              </div>
            </div>
          </div>

          {/* Error Display */}
          {errors.length > 0 && (
            <div className="mt-6 p-4 bg-red-50 border border-red-200 rounded-md">
              <h4 className="text-sm font-medium text-red-800 mb-2">
                Validation Errors:
              </h4>
              <ul className="text-sm text-red-700 space-y-1">
                {errors.map((error, index) => (
                  <li key={index}>• {error}</li>
                ))}
              </ul>
            </div>
          )}

          <div className="mt-6">
            <Button
              onClick={handleGeneratePeriodization}
              disabled={
                !offSeasonStartDate ||
                !prepStartDate ||
                !preCompStartDate ||
                !competitionStartDate ||
                !competitionEndDate ||
                isGenerating
              }
              className="flex items-center"
            >
              <Play className="h-4 w-4 mr-2" />
              {isGenerating ? "Generating..." : "Generate Periodization"}
            </Button>
          </div>
        </div>
      </Card>

      {/* Available Resources Summary */}
      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        <Card className="p-4">
          <div className="flex items-center">
            <BookOpen className="h-8 w-8 text-green-600" />
            <div className="ml-3">
              <p className="text-sm font-medium text-gray-600">
                Available Exercises
              </p>
              <p className="text-lg font-semibold text-gray-900">
                {allExercises.length}
              </p>
            </div>
          </div>
        </Card>

        <Card className="p-4">
          <div className="flex items-center">
            <Eye className="h-8 w-8 text-purple-600" />
            <div className="ml-3">
              <p className="text-sm font-medium text-gray-600">
                Available Visualizations
              </p>
              <p className="text-lg font-semibold text-gray-900">
                {allVisualizations.length}
              </p>
            </div>
          </div>
        </Card>
      </div>

      {/* Periodization Results */}
      {periodizationData.length > 0 && (
        <Card>
          <CardHeader
            title={`Periodization Schedule (${periodizationData.length} assignments)`}
          />
          <div className="p-6 pt-0">
            {/* Category summary split by Mental Toughness and Mental Wellness */}
            {(() => {
              // Skills for each type (from utils/periodization.ts)
              const mtSkills = ["B1", "B2", "B3", "C1", "C2", "C3", "C4", "C5"];
              const mwSkills = [
                "M",
                "P1",
                "P2",
                "P3",
                "E1",
                "E2",
                "R",
                "A",
                "L1",
                "L2",
                "L3",
                "U",
                "S",
              ];

              // Count per skill/category for each type
              const mtCounts: Record<string, number> = {};
              const mwCounts: Record<string, number> = {};

              periodizationData.forEach((entry) => {
                const id = entry.exerciseId?.toString().toLowerCase();
                let skill = "Other";
                if (id) {
                  const parts = id.split("-");
                  if (parts.length >= 2) {
                    skill = parts[1].toUpperCase();
                  }
                }
                if (entry.exerciseType === "MT exercise") {
                  mtCounts[skill] = (mtCounts[skill] || 0) + 1;
                } else if (entry.exerciseType === "MW exercise") {
                  mwCounts[skill] = (mwCounts[skill] || 0) + 1;
                }
              });

              return (
                <div className="mb-4">
                  <div className="mb-2 font-semibold text-orange-700 text-left">
                    Mental Toughness
                  </div>
                  <div className="flex flex-wrap gap-4 mb-4">
                    {mtSkills.map((skill) => (
                      <div
                        key={skill}
                        className="px-3 py-1 rounded bg-orange-100 text-sm font-medium text-orange-700"
                      >
                        {skill}:{" "}
                        <span className="font-bold">
                          {mtCounts[skill] || 0}
                        </span>
                      </div>
                    ))}
                  </div>
                  <div className="mb-2 font-semibold text-green-700 text-left">
                    Mental Wellness
                  </div>
                  <div className="flex flex-wrap gap-4">
                    {mwSkills.map((skill) => (
                      <div
                        key={skill}
                        className="px-3 py-1 rounded bg-green-100 text-sm font-medium text-green-700"
                      >
                        {skill}:{" "}
                        <span className="font-bold">
                          {mwCounts[skill] || 0}
                        </span>
                      </div>
                    ))}
                  </div>
                </div>
              );
            })()}
            <DataTable
              columns={columns}
              data={periodizationData}
              className="w-full"
            />
          </div>
        </Card>
      )}

      {/* Empty State */}
      {periodizationData.length === 0 &&
        offSeasonStartDate &&
        prepStartDate &&
        preCompStartDate &&
        competitionStartDate &&
        competitionEndDate && (
          <Card className="p-8">
            <div className="text-center">
              <Calendar className="mx-auto h-12 w-12 text-gray-400" />
              <h3 className="mt-2 text-sm font-medium text-gray-900">
                No periodization generated
              </h3>
              <p className="mt-1 text-sm text-gray-500">
                Click "Generate Periodization" to create an assignment schedule.
              </p>
            </div>
          </Card>
        )}
    </div>
  );
};

export default AdminPeriodization;
