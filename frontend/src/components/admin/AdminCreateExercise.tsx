import { useState, useRef } from "react";
import { QuestionInput as QuestionInputType } from "../../types/exercise.types";
import { Button } from "../ui/Button";
import QuestionPreview from "./QuestionPreview";
import QuestionInput from "./QuestionInput";
import { useAdmin } from "../../context/AdminContext";
import TextInput from "../ui/input/TextInput";
import RichTextEditor from "../ui/input/RichTextEditor";
import { uploadImageWithFallback } from "../../utils/imageUpload";
import { Download, Upload } from "lucide-react";
import DraggableList from "../ui/DraggableList";
import { v4 as uuid } from "uuid";

const AdminCreateExercise = () => {
  const { createExercise } = useAdmin();
  const fileInputRef = useRef<HTMLInputElement>(null);
  const [id, setId] = useState("");
  const [name, setName] = useState("");
  const [description, setDescription] = useState("");
  const [questions, setQuestions] = useState<QuestionInputType[]>([]);

  const addQuestion = () => {
    const newQuestion: QuestionInputType = {
      id: uuid(),
      prompt: "",
      type: "free_text",
      required: false,
      isEditing: true,
    };
    setQuestions((prevQuestions) => [...prevQuestions, newQuestion]);
  };

  const addTextBlock = () => {
    const newTextBlock: QuestionInputType = {
      id: uuid(),
      type: "text_block",
      content: "",
      title: "",
      isEditing: true,
    } as any;
    setQuestions((prevQuestions) => [...prevQuestions, newTextBlock]);
  };

  const handleReorderQuestions = (reorderedQuestions: QuestionInputType[]) => {
    setQuestions(reorderedQuestions);
  };

  const handleFileUpload = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (!file) return;

    const reader = new FileReader();
    reader.onload = (e) => {
      try {
        const json = JSON.parse(e.target?.result as string);
        if (json.questions) {
          setQuestions(
            json.questions.map((q: QuestionInputType) => ({
              ...q,
              isEditing: false,
            }))
          );
        }
        if (json.id) setId(json.id);
        if (json.name) setName(json.name);
        if (json.description) setDescription(json.description);
      } catch (error) {
        console.error("Error parsing JSON:", error);
        alert("Invalid JSON file format");
      }
    };
    reader.readAsText(file);
  };

  const handleDownload = () => {
    const data = {
      id,
      name,
      description,
      questions: questions.map(({ isEditing, ...q }) => q), // Remove isEditing flag
    };
    const blob = new Blob([JSON.stringify(data, null, 2)], {
      type: "application/json",
    });
    const url = URL.createObjectURL(blob);
    const a = document.createElement("a");
    a.href = url;
    a.download = `exercise_${id || "draft"}.json`;
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    URL.revokeObjectURL(url);
  };

  return (
    <div>
      <h1 className="text-2xl font-bold mb-4">Create Exercise</h1>
      <div className="space-y-4">
        <div className="flex items-center space-x-2">
          <div>
            <TextInput
              label="ID"
              value={id}
              onChange={setId}
              placeholder="Exercise ID"
            />
          </div>
          <TextInput
            label="Name"
            value={name}
            onChange={setName}
            placeholder="Exercise Name"
          />
        </div>
        <div>
          <RichTextEditor
            key="exercise-description-editor"
            label="Description"
            value={description}
            onChange={setDescription}
            placeholder="Enter exercise description with rich text formatting..."
            height="150px"
            enableImages={true}
            onImageUpload={uploadImageWithFallback}
          />

          {/* Debug info */}
          <div className="mt-2 p-2 bg-gray-100 text-xs">
            <strong>Debug:</strong> enableImages=true, onImageUpload=provided
          </div>
        </div>
        <div className="flex justify-between items-center">
          <h2 className="text-xl font-bold">Questions</h2>
          <div className="flex space-x-2">
            <input
              type="file"
              ref={fileInputRef}
              onChange={handleFileUpload}
              accept=".json"
              className="hidden"
            />
            <Button
              variant="outline"
              size="sm"
              onClick={() => fileInputRef.current?.click()}
            >
              <Upload className="h-4 w-4 mr-2" />
              Import JSON
            </Button>
            <Button
              variant="outline"
              size="sm"
              onClick={handleDownload}
              disabled={questions.length === 0}
            >
              <Download className="h-4 w-4 mr-2" />
              Export JSON
            </Button>
          </div>
        </div>
        <DraggableList
          items={questions}
          onReorder={handleReorderQuestions}
          getItemId={(_, index) => `question-${index}`}
          renderItem={(question, index) => (
            <div>
              <p className="font-medium text-gray-700 mb-2">
                {question.type === "text_block"
                  ? `Text Block ${index + 1}`
                  : `Question ${index + 1}`}
                :
              </p>
              {question.isEditing ? (
                <QuestionInput
                  question={question}
                  onChange={(updatedQuestion: QuestionInputType) => {
                    const updatedQuestions = [...questions];
                    updatedQuestions[index] = updatedQuestion;
                    setQuestions(updatedQuestions);
                  }}
                  onDelete={() => {
                    const updatedQuestions = questions.filter(
                      (_, i) => i !== index
                    );
                    setQuestions(updatedQuestions);
                  }}
                />
              ) : (
                <QuestionPreview
                  question={question}
                  setIsEditing={() => {
                    const updatedQuestions = [...questions];
                    updatedQuestions[index].isEditing = true;
                    setQuestions(updatedQuestions);
                  }}
                />
              )}
            </div>
          )}
        />
        <div className="flex space-x-4">
          <Button onClick={addQuestion}>Add Question</Button>
          <Button onClick={addTextBlock} variant="outline">
            Add Text Block
          </Button>
          <Button
            onClick={() => {
              console.log("Creating exercise with data:", {
                id,
                name,
                description,
                questions,
              });
              createExercise({
                id,
                name,
                description,
                questions: questions.map((q) => {
                  const { isEditing, ...rest } = q;
                  return rest;
                }),
              })
                .then(() => {
                  // Reset form after successful creation
                  setId("");
                  setName("");
                  setDescription("");
                  setQuestions([]);
                })
                .catch((error) => {
                  console.error("Error creating exercise:", error);
                });
            }}
          >
            Create Exercise
          </Button>
        </div>
      </div>
    </div>
  );
};

export default AdminCreateExercise;
