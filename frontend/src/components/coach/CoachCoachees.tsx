import { useEffect, useState } from "react";
import { PageHeader } from "../ui/PageHeader";
import { Card, CardHeader } from "../ui/Card";
import { useCoach } from "../../context/CoachContext";
import { LoadingSpinner } from "../ui/LoadingSpinner";
import { Button } from "../ui/Button";
import {
  Mail,
  User,
  Building,
  Edit3,
  Save,
  X,
  Calendar,
  CheckCircle,
  Clock,
  MessageSquare,
  Brain,
  Plus,
  TrendingUp,
} from "lucide-react";
import Tab, { TabContainer } from "../ui/Tab";
import EvaluationChart from "./EvaluationChart";
import EvaluationSummary from "./EvaluationSummary";
import EvaluationRadarChart from "./EvaluationRadarChart";
import CoacheeIPSVisualization from "./CoacheeIPSVisualization";
import CoacheePerformanceProfiling from "./CoacheePerformanceProfiling";
import { Link } from "react-router-dom";
import { CoacheeDetailsApiResponse } from "../../types/api/user.types";

const CoachCoachees = () => {
  const {
    myCoachees,
    coacheeDetails,
    loading,
    fetchMyCoachees,
    fetchCoacheeDetails,
    updateCoacheeNotes,
    fetchCoacheeEvaluationTrends,
    fetchLatestCoacheeEvaluation,
  } = useCoach();

  const [selectedCoacheeId, setSelectedCoacheeId] = useState<string | null>(
    null
  );

  useEffect(() => {
    fetchMyCoachees();
  }, []);

  const handleViewDetails = async (coacheeId: string) => {
    setSelectedCoacheeId(coacheeId);
    await Promise.all([
      fetchCoacheeDetails(coacheeId),
      fetchCoacheeEvaluationTrends(coacheeId),
      fetchLatestCoacheeEvaluation(coacheeId),
    ]);
  };

  if (loading.coachees) {
    return <LoadingSpinner />;
  }

  return (
    <div className="space-y-6">
      <PageHeader
        title="My Coachees"
        description="Manage and view details of your assigned coachees."
        actionButton={{
          label: "Assign New Coachee",
          to: "/dashboard/assign-coachee",
        }}
      />

      <div className="grid grid-cols-1 lg:grid-cols-2 lg:grid-cols-[450px_1fr] gap-6">
        {/* Coachees List */}
        <Card
          header={
            <CardHeader
              title="Assigned Coachees"
              description={`${myCoachees.length} coachees assigned to you`}
            />
          }
        >
          {myCoachees.length === 0 ? (
            <div className="text-center py-8">
              <User className="mx-auto h-12 w-12 text-gray-400" />
              <h3 className="mt-2 text-sm font-medium text-gray-900">
                No coachees assigned
              </h3>
              <p className="mt-1 text-sm text-gray-500">
                Get started by assigning your first coachee.
              </p>
              <div className="mt-6">
                <Button>
                  <a href="/dashboard/assign-coachee">Assign Coachee</a>
                </Button>
              </div>
            </div>
          ) : (
            <div className="space-y-3">
              {myCoachees.map((coachee) => (
                <div
                  key={coachee.id}
                  className={`p-4 border rounded-lg cursor-pointer transition-colors ${
                    selectedCoacheeId === coachee.id
                      ? "border-blue-500 bg-blue-50"
                      : "border-gray-200 hover:bg-gray-50"
                  }`}
                  onClick={() => handleViewDetails(coachee.id)}
                >
                  <div className="flex items-center justify-between">
                    <div className="flex items-center space-x-3">
                      <div className="flex-shrink-0">
                        <div className="h-10 w-10 bg-gray-300 rounded-full flex items-center justify-center">
                          <User className="h-6 w-6 text-gray-600" />
                        </div>
                      </div>
                      <div>
                        <h3 className="text-sm font-medium text-gray-900">
                          {coachee.firstName} {coachee.lastName}
                        </h3>
                        <div className="flex items-center text-sm text-gray-500">
                          <Mail className="h-4 w-4 mr-1" />
                          {coachee.email}
                        </div>
                      </div>
                    </div>
                    <div className="text-sm text-gray-500">
                      {coachee.organizationMemberships.length > 0 && (
                        <div className="flex items-center">
                          <Building className="h-4 w-4 mr-1" />
                          Organization
                        </div>
                      )}
                    </div>
                  </div>
                </div>
              ))}
            </div>
          )}
        </Card>

        {/* Coachee Details */}
        <Card
          header={
            <CardHeader
              title="Coachee Details"
              description={
                selectedCoacheeId
                  ? "Detailed information about the selected coachee"
                  : "Select a coachee to view details"
              }
            />
          }
        >
          {!selectedCoacheeId ? (
            <div className="text-center py-8">
              <User className="mx-auto h-12 w-12 text-gray-400" />
              <h3 className="mt-2 text-sm font-medium text-gray-900">
                No coachee selected
              </h3>
              <p className="mt-1 text-sm text-gray-500">
                Click on a coachee from the list to view their details.
              </p>
            </div>
          ) : !coacheeDetails ? (
            <LoadingSpinner />
          ) : (
            <CoacheeDetailsView
              coacheeDetails={coacheeDetails}
              onNotesUpdate={() => fetchCoacheeDetails(selectedCoacheeId!)}
              updateCoacheeNotes={updateCoacheeNotes}
            />
          )}
        </Card>
      </div>
    </div>
  );
};

interface CoacheeDetailsViewProps {
  coacheeDetails: CoacheeDetailsApiResponse;
  onNotesUpdate: () => void;
  updateCoacheeNotes: (coacheeId: string, notes: string) => Promise<void>;
}

const CoacheeDetailsView = ({
  coacheeDetails,
  onNotesUpdate,
  updateCoacheeNotes,
}: CoacheeDetailsViewProps) => {
  const { coacheeEvaluationTrends, latestCoacheeEvaluation } = useCoach();
  const [activeTab, setActiveTab] = useState<
    "overview" | "evaluations" | "ips" | "performance"
  >("overview");
  const [showRadarChart, setShowRadarChart] = useState(false);
  const [isEditingNotes, setIsEditingNotes] = useState(false);
  const [notesText, setNotesText] = useState(
    coacheeDetails.coacheeRelationships[0]?.coachNotes || ""
  );
  const [isSavingNotes, setIsSavingNotes] = useState(false);

  const handleSaveNotes = async () => {
    setIsSavingNotes(true);
    try {
      await updateCoacheeNotes(coacheeDetails.id, notesText);
      setIsEditingNotes(false);
      onNotesUpdate(); // Refresh the coachee details
    } catch (error) {
      console.error("Error saving notes:", error);
      // You might want to show an error message to the user here
    } finally {
      setIsSavingNotes(false);
    }
  };

  const handleCancelEdit = () => {
    setNotesText(coacheeDetails.coacheeRelationships[0]?.coachNotes || "");
    setIsEditingNotes(false);
  };
  return (
    <div className="space-y-6">
      {/* Tabs */}
      <TabContainer>
        <Tab
          title="Overview"
          isActive={activeTab === "overview"}
          onClick={() => setActiveTab("overview")}
        />
        <Tab
          title="B3-5C Evaluations"
          isActive={activeTab === "evaluations"}
          onClick={() => setActiveTab("evaluations")}
        />
        <Tab
          title="IPS Records"
          isActive={activeTab === "ips"}
          onClick={() => setActiveTab("ips")}
        />
        <Tab
          title="Performance Profiling"
          isActive={activeTab === "performance"}
          onClick={() => setActiveTab("performance")}
        />
      </TabContainer>

      {activeTab === "overview" && (
        <div className="space-y-6">
          {/* Basic Information */}
          <div>
            <h4 className="text-lg font-medium text-gray-900 mb-3">
              Basic Information
            </h4>
            <div className="grid grid-cols-1 gap-3">
              <div>
                <label className="text-sm font-medium text-gray-500">
                  Name
                </label>
                <p className="text-sm text-gray-900">
                  {coacheeDetails.firstName} {coacheeDetails.lastName}
                </p>
              </div>
              <div>
                <label className="text-sm font-medium text-gray-500">
                  Email
                </label>
                <p className="text-sm text-gray-900">{coacheeDetails.email}</p>
              </div>
              <div>
                <label className="text-sm font-medium text-gray-500">
                  Member Since
                </label>
                <p className="text-sm text-gray-900">
                  {new Date(coacheeDetails.createdAt).toLocaleDateString()}
                </p>
              </div>
            </div>
          </div>

          {/* Organizations */}
          {coacheeDetails.organizationMemberships.length > 0 && (
            <div>
              <h4 className="text-lg font-medium text-gray-900 mb-3">
                Organizations
              </h4>
              <div className="space-y-2">
                {coacheeDetails.organizationMemberships.map(
                  (membership, index) => (
                    <div key={index} className="flex items-center space-x-2">
                      <Building className="h-4 w-4 text-gray-500" />
                      <span className="text-sm text-gray-900">
                        {membership.organization.name}
                      </span>
                    </div>
                  )
                )}
              </div>
            </div>
          )}

          {/* Coach Notes */}
          <div>
            <div className="flex items-center justify-between mb-3">
              <h4 className="text-lg font-medium text-gray-900">Coach Notes</h4>
              {!isEditingNotes && (
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => setIsEditingNotes(true)}
                  className="flex items-center space-x-1"
                >
                  <Edit3 className="h-4 w-4" />
                  <span>Edit</span>
                </Button>
              )}
            </div>

            {isEditingNotes ? (
              <div className="space-y-3">
                <textarea
                  value={notesText}
                  onChange={(e) => setNotesText(e.target.value)}
                  className="w-full p-3 border border-gray-300 rounded-lg resize-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                  rows={4}
                  placeholder="Add your notes about this coachee..."
                />
                <div className="flex space-x-2">
                  <Button
                    onClick={handleSaveNotes}
                    disabled={isSavingNotes}
                    className="flex items-center space-x-1"
                  >
                    <Save className="h-4 w-4" />
                    <span>{isSavingNotes ? "Saving..." : "Save"}</span>
                  </Button>
                  <Button
                    variant="outline"
                    onClick={handleCancelEdit}
                    disabled={isSavingNotes}
                    className="flex items-center space-x-1"
                  >
                    <X className="h-4 w-4" />
                    <span>Cancel</span>
                  </Button>
                </div>
              </div>
            ) : (
              <div className="bg-gray-50 p-3 rounded-lg">
                <p className="text-sm text-gray-900">
                  {coacheeDetails.coacheeRelationships[0]?.coachNotes ||
                    "No notes available"}
                </p>
              </div>
            )}
          </div>

          {/* Assignments Summary */}
          <div>
            <div className="flex items-center justify-between mb-3">
              <h4 className="text-lg font-medium text-gray-900">
                Assignments Summary
              </h4>
              <Button
                variant="outline"
                size="sm"
                onClick={onNotesUpdate}
                className="flex items-center space-x-1"
                title="Refresh assignments data"
              >
                <TrendingUp className="h-4 w-4" />
                <span>Refresh</span>
              </Button>
            </div>
            <div className="grid grid-cols-2 gap-4 mb-6">
              <div className="bg-blue-50 p-3 rounded-lg">
                <p className="text-sm font-medium text-blue-900">
                  Total Assignments
                </p>
                <p className="text-2xl font-semibold text-blue-600">
                  {coacheeDetails.receivedAssignments.length}
                </p>
              </div>
              <div className="bg-green-50 p-3 rounded-lg">
                <p className="text-sm font-medium text-green-900">Completed</p>
                <p className="text-2xl font-semibold text-green-600">
                  {
                    coacheeDetails.receivedAssignments.filter(
                      (a) => a.status === "COMPLETED"
                    ).length
                  }
                </p>
              </div>
            </div>
          </div>

          {/* Recent Assignments with Feedback */}
          <div>
            <h4 className="text-lg font-medium text-gray-900 mb-3">
              Recent Assignments
            </h4>
            {coacheeDetails.receivedAssignments.length === 0 &&
            coacheeDetails.receivedVisAssignments.length === 0 ? (
              <div className="text-center py-8 bg-gray-50 rounded-lg">
                <MessageSquare className="mx-auto h-12 w-12 text-gray-400" />
                <h3 className="mt-2 text-sm font-medium text-gray-900">
                  No assignments yet
                </h3>
                <p className="mt-1 text-sm text-gray-500">
                  Create an assignment to get started.
                </p>
              </div>
            ) : (
              <div className="space-y-4">
                {[
                  // Combine regular assignments and visualization assignments
                  ...coacheeDetails.receivedAssignments.map((assignment) => ({
                    ...assignment,
                    type: "exercise" as const,
                    title: assignment.exercise.name,
                    description: assignment.exercise.description,
                  })),
                  ...coacheeDetails.receivedVisAssignments.map(
                    (assignment) => ({
                      ...assignment,
                      type: "visualization" as const,
                      title: assignment.visualization.title,
                      description: assignment.visualization.description,
                      coachFeedback: null, // Visualization assignments don't have feedback yet
                      feedbackAt: null,
                      aiSummary: null,
                    })
                  ),
                ]
                  .sort(
                    (a, b) =>
                      new Date(b.createdAt).getTime() -
                      new Date(a.createdAt).getTime()
                  )
                  .slice(0, 5) // Show only the 5 most recent assignments
                  .map((assignment) => (
                    <div
                      key={assignment.id}
                      className="border border-gray-200 rounded-lg p-4"
                    >
                      <div className="flex items-start justify-between mb-3">
                        <div className="flex-1">
                          <div className="flex items-center space-x-2 mb-1">
                            <h5 className="text-sm font-medium text-gray-900">
                              {assignment.title}
                            </h5>
                            <span
                              className={`px-2 py-1 text-xs font-medium rounded-full ${
                                assignment.type === "exercise"
                                  ? "bg-blue-100 text-blue-800"
                                  : "bg-purple-100 text-purple-800"
                              }`}
                            >
                              {assignment.type === "exercise"
                                ? "Exercise"
                                : "Imagery Exercise"}
                            </span>
                          </div>
                          <p className="text-xs text-gray-500 mt-1">
                            {assignment.description
                              ? (() => {
                                  // Strip HTML tags for preview
                                  const plainText =
                                    assignment.description.replace(
                                      /<[^>]*>/g,
                                      ""
                                    );
                                  return plainText.length > 50
                                    ? `${plainText.substring(0, 50)}...`
                                    : plainText;
                                })()
                              : "No description"}
                          </p>
                        </div>
                        <div className="flex items-center space-x-2 ml-4">
                          <div className="flex items-center space-x-1">
                            <Calendar className="h-4 w-4 text-gray-400" />
                            <span className="text-xs text-gray-500">
                              {new Date(
                                assignment.createdAt
                              ).toLocaleDateString()}
                            </span>
                          </div>
                          <div
                            className={`flex items-center space-x-1 px-2 py-1 rounded-full text-xs font-medium ${
                              assignment.status === "COMPLETED"
                                ? "bg-green-100 text-green-800"
                                : assignment.status === "IN_PROGRESS"
                                ? "bg-yellow-100 text-yellow-800"
                                : "bg-gray-100 text-gray-800"
                            }`}
                          >
                            {assignment.status === "COMPLETED" ? (
                              <CheckCircle className="h-3 w-3" />
                            ) : (
                              <Clock className="h-3 w-3" />
                            )}
                            <span>{assignment.status.replace("_", " ")}</span>
                          </div>
                        </div>
                      </div>

                      {assignment.coachFeedback && (
                        <div className="mt-3 p-3 bg-blue-50 rounded-lg">
                          <div className="flex items-center space-x-1 mb-2">
                            <MessageSquare className="h-4 w-4 text-blue-600" />
                            <span className="text-sm font-medium text-blue-900">
                              Coach Feedback
                            </span>
                            {assignment.feedbackAt && (
                              <span className="text-xs text-blue-600">
                                •{" "}
                                {new Date(
                                  assignment.feedbackAt
                                ).toLocaleDateString()}
                              </span>
                            )}
                          </div>
                          <p className="text-sm text-blue-800">
                            {assignment.coachFeedback}
                          </p>
                        </div>
                      )}

                      {assignment.aiSummary && (
                        <div className="mt-3 p-3 bg-purple-50 rounded-lg">
                          <div className="flex items-center space-x-1 mb-2">
                            <MessageSquare className="h-4 w-4 text-purple-600" />
                            <span className="text-sm font-medium text-purple-900">
                              AI Summary
                            </span>
                          </div>
                          <p className="text-sm text-purple-800">
                            {assignment.aiSummary}
                          </p>
                        </div>
                      )}

                      {assignment.dueDate && (
                        <div className="mt-3 text-xs text-gray-500">
                          Due:{" "}
                          {new Date(assignment.dueDate).toLocaleDateString()}
                        </div>
                      )}
                    </div>
                  ))}
              </div>
            )}
          </div>
        </div>
      )}

      {activeTab === "evaluations" && (
        <div className="space-y-6">
          {/* Evaluation Actions */}
          <div className="flex items-center justify-between">
            <h4 className="text-lg font-medium text-gray-900">
              B3-5C Mental Toughness Evaluations
            </h4>
            <div className="flex items-center space-x-3">
              {latestCoacheeEvaluation && (
                <div className="flex items-center space-x-2">
                  <button
                    onClick={() => setShowRadarChart(false)}
                    className={`px-3 py-1 text-sm rounded-md ${
                      !showRadarChart
                        ? "bg-blue-100 text-blue-700"
                        : "bg-gray-100 text-gray-600 hover:bg-gray-200"
                    }`}
                  >
                    Summary
                  </button>
                  <button
                    onClick={() => setShowRadarChart(true)}
                    className={`px-3 py-1 text-sm rounded-md ${
                      showRadarChart
                        ? "bg-blue-100 text-blue-700"
                        : "bg-gray-100 text-gray-600 hover:bg-gray-200"
                    }`}
                  >
                    Radar Chart
                  </button>
                </div>
              )}
              <Link
                to={`/dashboard/create-evaluation?coacheeId=${coacheeDetails.id}`}
                className="inline-flex items-center space-x-2 px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
              >
                <Plus className="h-4 w-4" />
                <span>Create Evaluation</span>
              </Link>
            </div>
          </div>

          {/* Latest Evaluation Summary */}
          {latestCoacheeEvaluation ? (
            <div className="space-y-4">
              <h5 className="text-md font-medium text-gray-900">
                Latest Evaluation
              </h5>
              {showRadarChart ? (
                <div className="bg-white border rounded-lg p-6">
                  <div className="text-center mb-4">
                    <h6 className="text-md font-medium text-gray-900">
                      {latestCoacheeEvaluation.title ||
                        "B3-5C Mental Toughness Evaluation"}
                    </h6>
                    <p className="text-sm text-gray-600">
                      Created on{" "}
                      {new Date(
                        latestCoacheeEvaluation.createdAt
                      ).toLocaleDateString()}
                    </p>
                  </div>
                  <EvaluationRadarChart
                    evaluation={latestCoacheeEvaluation}
                    size={280}
                    showLabels={true}
                    showValues={true}
                  />
                </div>
              ) : (
                <EvaluationSummary
                  evaluation={latestCoacheeEvaluation}
                  compact={true}
                />
              )}
            </div>
          ) : (
            <div className="text-center py-8 bg-gray-50 rounded-lg">
              <Brain className="mx-auto h-12 w-12 text-gray-400" />
              <h3 className="mt-2 text-sm font-medium text-gray-900">
                No evaluations yet
              </h3>
              <p className="mt-1 text-sm text-gray-500">
                Create the first B3-5C evaluation for this coachee.
              </p>
            </div>
          )}

          {/* Evaluation Trends Chart */}
          {coacheeEvaluationTrends.length > 0 && (
            <div className="space-y-4">
              <div className="flex items-center space-x-2">
                <TrendingUp className="h-5 w-5 text-blue-600" />
                <h5 className="text-md font-medium text-gray-900">
                  Progress Over Time
                </h5>
              </div>
              <EvaluationChart
                trends={coacheeEvaluationTrends}
                height={300}
                width={700}
              />
            </div>
          )}
        </div>
      )}

      {activeTab === "ips" && (
        <CoacheeIPSVisualization
          coacheeId={coacheeDetails.id}
          coacheeName={
            `${coacheeDetails.firstName || ""} ${
              coacheeDetails.lastName || ""
            }`.trim() || coacheeDetails.email
          }
        />
      )}

      {activeTab === "performance" && (
        <CoacheePerformanceProfiling
          coacheeId={coacheeDetails.id}
          coacheeName={
            `${coacheeDetails.firstName || ""} ${
              coacheeDetails.lastName || ""
            }`.trim() || coacheeDetails.email
          }
        />
      )}
    </div>
  );
};

export default CoachCoachees;
