import { useEffect } from "react";
import { <PERSON> } from "react-router-dom";
import { PageHeader } from "../ui/PageHeader";
import { Card, CardHeader } from "../ui/Card";
import { useCoach } from "../../context/CoachContext";
import { LoadingSpinner } from "../ui/LoadingSpinner";
import { Users, BookOpen, Plus, UserPlus, Eye } from "lucide-react";

const CoachHome = () => {
  const {
    myCoachees,
    allExercises,
    myAssignments,
    loading,
    fetchMyCoachees,
    fetchAllExercises,
    fetchMyAssignments,
  } = useCoach();

  useEffect(() => {
    // Fetch initial data when component mounts
    fetchMyCoachees();
    fetchAllExercises();
    fetchMyAssignments();
  }, []);

  const isLoading =
    loading.coachees || loading.exercises || loading.assignments;

  // Calculate statistics
  const totalCoachees = myCoachees.length;
  const totalExercises = allExercises.length;
  const pendingAssignments = myAssignments.filter(
    (a) => a.status === "PENDING"
  ).length;
  const completedAssignments = myAssignments.filter(
    (a) => a.status === "COMPLETED"
  ).length;

  if (isLoading) {
    return <LoadingSpinner />;
  }

  return (
    <div className="space-y-6">
      <PageHeader
        title="Coach Dashboard"
        description="Manage your coachees and track their progress."
      />

      {/* Statistics Cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        <Card>
          <div className="flex items-center">
            <div className="flex-shrink-0">
              <Users className="h-8 w-8 text-blue-600" />
            </div>
            <div className="ml-4">
              <p className="text-sm font-medium text-gray-500">
                Total Coachees
              </p>
              <p className="text-2xl font-semibold text-gray-900">
                {totalCoachees}
              </p>
            </div>
          </div>
        </Card>

        <Card>
          <div className="flex items-center">
            <div className="flex-shrink-0">
              <BookOpen className="h-8 w-8 text-green-600" />
            </div>
            <div className="ml-4">
              <p className="text-sm font-medium text-gray-500">
                Available Exercises
              </p>
              <p className="text-2xl font-semibold text-gray-900">
                {totalExercises}
              </p>
            </div>
          </div>
        </Card>

        <Card>
          <div className="flex items-center">
            <div className="flex-shrink-0">
              <div className="h-8 w-8 bg-yellow-100 rounded-full flex items-center justify-center">
                <span className="text-yellow-600 font-semibold">P</span>
              </div>
            </div>
            <div className="ml-4">
              <p className="text-sm font-medium text-gray-500">
                Pending Assignments
              </p>
              <p className="text-2xl font-semibold text-gray-900">
                {pendingAssignments}
              </p>
            </div>
          </div>
        </Card>

        <Card>
          <div className="flex items-center">
            <div className="flex-shrink-0">
              <div className="h-8 w-8 bg-green-100 rounded-full flex items-center justify-center">
                <span className="text-green-600 font-semibold">✓</span>
              </div>
            </div>
            <div className="ml-4">
              <p className="text-sm font-medium text-gray-500">
                Completed Assignments
              </p>
              <p className="text-2xl font-semibold text-gray-900">
                {completedAssignments}
              </p>
            </div>
          </div>
        </Card>
      </div>

      {/* Quick Actions */}
      <Card
        header={
          <CardHeader
            title="Quick Actions"
            description="Common tasks you can perform"
          />
        }
      >
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
          <Link
            to="/dashboard/coachees"
            className="flex items-center p-4 border border-gray-200 rounded-lg hover:bg-gray-50 transition-colors"
          >
            <Users className="h-6 w-6 text-blue-600 mr-3" />
            <div>
              <h3 className="text-sm font-medium text-gray-900">
                View My Coachees
              </h3>
              <p className="text-sm text-gray-500">See all assigned coachees</p>
            </div>
          </Link>

          <Link
            to="/dashboard/assign-coachee"
            className="flex items-center p-4 border border-gray-200 rounded-lg hover:bg-gray-50 transition-colors"
          >
            <UserPlus className="h-6 w-6 text-green-600 mr-3" />
            <div>
              <h3 className="text-sm font-medium text-gray-900">
                Assign New Coachee
              </h3>
              <p className="text-sm text-gray-500">
                Add a coachee to your list
              </p>
            </div>
          </Link>

          <Link
            to="/dashboard/exercises"
            className="flex items-center p-4 border border-gray-200 rounded-lg hover:bg-gray-50 transition-colors"
          >
            <BookOpen className="h-6 w-6 text-purple-600 mr-3" />
            <div>
              <h3 className="text-sm font-medium text-gray-900">
                Browse Exercises
              </h3>
              <p className="text-sm text-gray-500">View available exercises</p>
            </div>
          </Link>

          <Link
            to="/dashboard/create-assignment"
            className="flex items-center p-4 border border-gray-200 rounded-lg hover:bg-gray-50 transition-colors"
          >
            <Plus className="h-6 w-6 text-orange-600 mr-3" />
            <div>
              <h3 className="text-sm font-medium text-gray-900">
                Create Assignment
              </h3>
              <p className="text-sm text-gray-500">
                Assign exercises to coachees
              </p>
            </div>
          </Link>

          <Link
            to="/dashboard/visualizations"
            className="flex items-center p-4 border border-gray-200 rounded-lg hover:bg-gray-50 transition-colors"
          >
            <Eye className="h-6 w-6 text-indigo-600 mr-3" />
            <div>
              <h3 className="text-sm font-medium text-gray-900">
                Manage Imagery Exercises
              </h3>
              <p className="text-sm text-gray-500">
                Create and assign imagery exercises
              </p>
            </div>
          </Link>
        </div>
      </Card>

      {/* Recent Activity */}
      <Card
        header={
          <CardHeader
            title="Recent Assignments"
            description="Latest assignments you've created"
          />
        }
      >
        {myAssignments.length === 0 ? (
          <p className="text-gray-500 text-center py-4">
            No assignments created yet.
          </p>
        ) : (
          <div className="space-y-3">
            {myAssignments.slice(0, 5).map((assignment) => (
              <div
                key={assignment.id}
                className="flex items-center justify-between p-3 border border-gray-200 rounded-lg"
              >
                <div>
                  <h4 className="text-sm font-medium text-gray-900">
                    {assignment.exercise.name}
                  </h4>
                  <p className="text-sm text-gray-500">
                    Assigned to coachee • {assignment.status}
                  </p>
                </div>
                <div className="text-sm text-gray-500">
                  {assignment.dueDate &&
                    new Date(assignment.dueDate).toLocaleDateString()}
                </div>
              </div>
            ))}
          </div>
        )}
      </Card>
    </div>
  );
};

export default CoachHome;
