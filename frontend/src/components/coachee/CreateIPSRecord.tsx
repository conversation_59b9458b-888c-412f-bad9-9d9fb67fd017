import React, { useState } from "react";
import { <PERSON>, CardHeader } from "../ui/Card";
import { Button } from "../ui/Button";
import { PageHeader } from "../ui/PageHeader";
import { SliderWithValue } from "../ui/form/SliderWithValue";
import { Trophy, TrendingUp, AlertCircle } from "lucide-react";
import { CreateIPSRecordRequest } from "../../types/api/ips.types";
import { createIPSRecord } from "../../api/ips";
import { formatDateTimeLocal } from "../../utils/dateUtils";

const CreateIPSRecord = () => {
  const [formData, setFormData] = useState({
    competitionDateTime: new Date(),
    competitionName: "",
    performanceScore: 5,
    arousalScore: 5,
    concentration: "",
    confidence: "",
  });
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [success, setSuccess] = useState(false);

  const handleInputChange = (
    e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>
  ) => {
    const { name, value, type } = e.target;
    setFormData((prev) => ({
      ...prev,
      [name]: type === "number" ? parseInt(value, 10) : value,
    }));
  };

  const handleDateChange = (value: string) => {
    setFormData((prev) => ({
      ...prev,
      competitionDateTime: new Date(value),
    }));
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsSubmitting(true);
    setError(null);
    setSuccess(false);

    try {
      // Validation
      if (!formData.competitionName.trim()) {
        throw new Error("Competition name is required");
      }

      if (formData.performanceScore < 0 || formData.performanceScore > 10) {
        throw new Error("Performance score must be between 0 and 10");
      }

      if (formData.arousalScore < 0 || formData.arousalScore > 10) {
        throw new Error("Arousal score must be between 0 and 10");
      }

      const requestData: CreateIPSRecordRequest = {
        dateTime: new Date().toISOString(), // Use current time
        competitionDateTime: formData.competitionDateTime.toISOString(),
        competitionName: formData.competitionName.trim(),
        performanceScore: formData.performanceScore,
        arousalScore: formData.arousalScore,
        concentration: formData.concentration.trim() || undefined,
        confidence: formData.confidence.trim() || undefined,
      };

      await createIPSRecord(requestData);

      setSuccess(true);
      // Reset form
      setFormData({
        competitionDateTime: new Date(),
        competitionName: "",
        performanceScore: 5,
        arousalScore: 5,
        concentration: "",
        confidence: "",
      });
    } catch (err) {
      setError(err instanceof Error ? err.message : "An error occurred");
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <div className="space-y-6">
      <PageHeader
        title="Create IPS Record"
        description="Record your Ideal Performance State data for competition analysis"
      />

      {error && (
        <div className="bg-red-50 border border-red-200 rounded-md p-4">
          <div className="flex">
            <AlertCircle className="h-5 w-5 text-red-400" />
            <div className="ml-3">
              <p className="text-sm text-red-800">{error}</p>
            </div>
          </div>
        </div>
      )}

      {success && (
        <div className="bg-green-50 border border-green-200 rounded-md p-4">
          <div className="flex">
            <div className="flex-shrink-0">
              <svg
                className="h-5 w-5 text-green-400"
                viewBox="0 0 20 20"
                fill="currentColor"
              >
                <path
                  fillRule="evenodd"
                  d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z"
                  clipRule="evenodd"
                />
              </svg>
            </div>
            <div className="ml-3">
              <p className="text-sm text-green-800">
                IPS record created successfully!
              </p>
            </div>
          </div>
        </div>
      )}

      <Card>
        <CardHeader title="Performance State Details" />
        <form onSubmit={handleSubmit} className="p-6 pt-0 space-y-6">
          {/* Competition Date & Time */}
          <div>
            <label
              htmlFor="competitionDateTime"
              className="block text-sm font-medium text-gray-700 mb-2"
            >
              <Trophy className="inline h-4 w-4 mr-1" />
              Competition Date & Time
            </label>
            <input
              type="datetime-local"
              id="competitionDateTime"
              name="competitionDateTime"
              value={formatDateTimeLocal(formData.competitionDateTime)}
              onChange={(e) => handleDateChange(e.target.value)}
              className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500"
              required
            />
          </div>

          {/* Competition Name */}
          <div>
            <label
              htmlFor="competitionName"
              className="block text-sm font-medium text-gray-700 mb-2"
            >
              Competition Name
            </label>
            <input
              type="text"
              id="competitionName"
              name="competitionName"
              value={formData.competitionName}
              onChange={handleInputChange}
              placeholder="e.g., Regional Championship, Practice Match"
              className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500"
              required
            />
          </div>

          {/* Performance Score */}
          <div>
            <SliderWithValue
              min={1}
              max={10}
              step={0.1}
              value={formData.performanceScore}
              onChange={(val) =>
                setFormData((prev) => ({ ...prev, performanceScore: val }))
              }
              disabled={isSubmitting}
              label={
                <>
                  <span>
                    <TrendingUp className="inline h-4 w-4 mr-1" />
                    Performance Score (1-10)
                  </span>
                  <p className="text-sm text-gray-500 mb-3">
                    Rate your overall performance level during the competition
                  </p>
                </>
              }
              labels={{
                1: "Poor",
                5: "Average",
                10: "Great",
              }}
            />
          </div>
          <br />
          {/* Arousal Score */}
          <div>
            <SliderWithValue
              min={1}
              max={10}
              step={0.1}
              value={formData.arousalScore}
              onChange={(val) =>
                setFormData((prev) => ({ ...prev, arousalScore: val }))
              }
              disabled={isSubmitting}
              label={
                <>
                  <span>Arousal Score (1-10)</span>
                  <p className="text-sm text-gray-500 mb-3">
                    Rate your energy/activation level during the competition
                  </p>
                </>
              }
              labels={{
                1: "Lethargic / low energy",
                5: "Mid-level",
                10: "Panic",
              }}
            />
          </div>

          {/* Concentration */}
          <div>
            <label
              htmlFor="concentration"
              className="block text-sm font-medium text-gray-700 mb-2"
            >
              Concentration
            </label>
            <textarea
              id="concentration"
              name="concentration"
              value={formData.concentration}
              onChange={handleInputChange}
              placeholder="What were you focusing on or thinking about?"
              rows={3}
              className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500"
            />
          </div>

          {/* Confidence */}
          <div>
            <label
              htmlFor="confidence"
              className="block text-sm font-medium text-gray-700 mb-2"
            >
              Confidence
            </label>
            <textarea
              id="confidence"
              name="confidence"
              value={formData.confidence}
              onChange={handleInputChange}
              placeholder="Describe your confidence going into & during performance"
              rows={3}
              className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500"
            />
          </div>

          {/* Submit Button */}
          <div className="flex justify-end">
            <Button type="submit" disabled={isSubmitting} className="px-6 py-2">
              {isSubmitting ? "Creating..." : "Create IPS Record"}
            </Button>
          </div>
        </form>
      </Card>
    </div>
  );
};

export default CreateIPSRecord;
