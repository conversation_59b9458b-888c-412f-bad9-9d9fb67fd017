import React, { useState, useRef, useEffect } from "react";

interface DateRangeBarProps {
  totalStartDate: string;
  totalEndDate: string;
  rangeStartWeek: number;
  rangeEndWeek: number;
  onRangeChange: (startWeek: number, endWeek: number) => void;
  title?: string;
  color?: string;
}

export const DateRangeBar: React.FC<DateRangeBarProps> = ({
  totalStartDate,
  totalEndDate,
  rangeStartWeek,
  rangeEndWeek,
  onRangeChange,
  title,
  color = "bg-purple-500",
}) => {
  const containerRef = useRef<HTMLDivElement>(null);
  const [isDragging, setIsDragging] = useState<"start" | "end" | null>(null);
  const [dragStartX, setDragStartX] = useState(0);
  const [dragStartValues, setDragStartValues] = useState({ start: 0, end: 0 });

  // Calculate total weeks between start and end dates
  const totalWeeks = React.useMemo(() => {
    if (!totalStartDate || !totalEndDate) return 0;
    const start = new Date(totalStartDate);
    const end = new Date(totalEndDate);
    return Math.ceil(
      (end.getTime() - start.getTime()) / (7 * 24 * 60 * 60 * 1000)
    );
  }, [totalStartDate, totalEndDate]);

  // Calculate date strings for display
  const getDateForWeek = (week: number) => {
    if (!totalStartDate) return "";
    const startDate = new Date(totalStartDate);
    const targetDate = new Date(startDate);
    targetDate.setDate(startDate.getDate() + (week - 1) * 7);
    return targetDate.toLocaleDateString();
  };

  const handleMouseDown = (e: React.MouseEvent, handle: "start" | "end") => {
    e.preventDefault();
    setIsDragging(handle);
    setDragStartX(e.clientX);
    setDragStartValues({ start: rangeStartWeek, end: rangeEndWeek });
  };

  const handleMouseMove = (e: MouseEvent) => {
    if (!isDragging || !containerRef.current) return;

    const rect = containerRef.current.getBoundingClientRect();
    const containerWidth = rect.width;
    const deltaX = e.clientX - dragStartX;
    const deltaWeeks = Math.round((deltaX / containerWidth) * totalWeeks);

    let newStartWeek = rangeStartWeek;
    let newEndWeek = rangeEndWeek;

    if (isDragging === "start") {
      newStartWeek = Math.max(
        1,
        Math.min(rangeEndWeek - 1, dragStartValues.start + deltaWeeks)
      );
    } else if (isDragging === "end") {
      newEndWeek = Math.min(
        totalWeeks,
        Math.max(rangeStartWeek + 1, dragStartValues.end + deltaWeeks)
      );
    }

    if (newStartWeek !== rangeStartWeek || newEndWeek !== rangeEndWeek) {
      onRangeChange(newStartWeek, newEndWeek);
    }
  };

  const handleMouseUp = () => {
    setIsDragging(null);
    setDragStartX(0);
    setDragStartValues({ start: 0, end: 0 });
  };

  useEffect(() => {
    if (isDragging) {
      document.addEventListener("mousemove", handleMouseMove);
      document.addEventListener("mouseup", handleMouseUp);
      return () => {
        document.removeEventListener("mousemove", handleMouseMove);
        document.removeEventListener("mouseup", handleMouseUp);
      };
    }
  }, [
    isDragging,
    dragStartX,
    dragStartValues,
    totalWeeks,
    rangeStartWeek,
    rangeEndWeek,
  ]);

  if (totalWeeks === 0) {
    return (
      <div className="text-center text-gray-500 py-4">
        Please select both start and end dates to see the {title?.toLowerCase()}{" "}
        range.
      </div>
    );
  }

  const startPercentage = ((rangeStartWeek - 1) / totalWeeks) * 100;
  const endPercentage = (rangeEndWeek / totalWeeks) * 100;
  const rangeWidth = endPercentage - startPercentage;

  return (
    <div className="space-y-4">
      <div className="flex justify-between items-center">
        <h4 className="text-md font-medium text-gray-900">{title}</h4>
        <div className="text-sm text-gray-600">
          Week {rangeStartWeek} - {rangeEndWeek} (
          {rangeEndWeek - rangeStartWeek + 1} weeks)
        </div>
      </div>

      <div
        ref={containerRef}
        className="relative h-12 bg-gray-200 rounded-lg overflow-hidden cursor-pointer select-none"
      >
        {/* Background timeline */}
        <div className="absolute top-0 left-0 w-full h-full flex items-center justify-center text-xs text-gray-500">
          Total: {totalWeeks} weeks
        </div>

        {/* Active range */}
        <div
          className={`absolute top-0 h-full ${color} flex items-center justify-center text-white font-medium text-sm transition-all duration-200`}
          style={{
            left: `${startPercentage}%`,
            width: `${rangeWidth}%`,
          }}
        >
          <div className="text-center">
            <div className="text-xs opacity-90">
              {rangeEndWeek - rangeStartWeek + 1} weeks
            </div>
          </div>
        </div>

        {/* Start handle */}
        <div
          className="absolute top-0 h-full w-3 bg-white border-2 border-gray-400 cursor-col-resize hover:bg-gray-100 transition-colors duration-200 z-10 flex items-center justify-center"
          style={{
            left: `calc(${startPercentage}% - 6px)`,
          }}
          onMouseDown={(e) => handleMouseDown(e, "start")}
        >
          <div className="w-1 h-6 bg-gray-400 rounded"></div>
        </div>

        {/* End handle */}
        <div
          className="absolute top-0 h-full w-3 bg-white border-2 border-gray-400 cursor-col-resize hover:bg-gray-100 transition-colors duration-200 z-10 flex items-center justify-center"
          style={{
            left: `calc(${endPercentage}% - 6px)`,
          }}
          onMouseDown={(e) => handleMouseDown(e, "end")}
        >
          <div className="w-1 h-6 bg-gray-400 rounded"></div>
        </div>
      </div>

      {/* Date range display */}
      <div className="flex justify-between text-sm text-gray-600">
        <span>Start: {getDateForWeek(rangeStartWeek)}</span>
        <span>End: {getDateForWeek(rangeEndWeek)}</span>
      </div>
    </div>
  );
};
