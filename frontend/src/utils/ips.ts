import { IPSChartDataPoint } from "../types/api/ips.types";

// Get magic number range (arousal range for optimal performance)
export const getMagicNumberRange = (
  dataPoints: IPSChartDataPoint[]
): {
  optimal: number;
  lowerBound: number;
  upperBound: number;
  range: string;
  display: string;
} | null => {
  if (!dataPoints || dataPoints.length < 3) return null;

  // Calculate standard deviation of arousal scores
  const arousalValues = dataPoints.map((pt) => pt.arousal);
  const arousalMean =
    arousalValues.reduce((sum, val) => sum + val, 0) / arousalValues.length;
  const arousalVariance =
    arousalValues.reduce(
      (sum, val) => sum + Math.pow(val - arousalMean, 2),
      0
    ) / arousalValues.length;
  const arousalStdDev = Math.sqrt(arousalVariance);

  // Least squares fit for y = ax^2 + bx + c
  const n = dataPoints.length;
  let sumX = 0,
    sumX2 = 0,
    sumX3 = 0,
    sumX4 = 0;
  let sumY = 0,
    sumXY = 0,
    sumX2Y = 0;
  for (const pt of dataPoints) {
    const x = pt.arousal;
    const y = pt.performance;
    sumX += x;
    sumX2 += x * x;
    sumX3 += x * x * x;
    sumX4 += x * x * x * x;
    sumY += y;
    sumXY += x * y;
    sumX2Y += x * x * y;
  }
  const A = [
    [n, sumX, sumX2],
    [sumX, sumX2, sumX3],
    [sumX2, sumX3, sumX4],
  ];
  const B = [sumY, sumXY, sumX2Y];
  function solve(A: number[][], B: number[]): number[] {
    const m = A.length;
    for (let k = 0; k < m; k++) {
      let maxRow = k;
      for (let i = k + 1; i < m; i++) {
        if (Math.abs(A[i][k]) > Math.abs(A[maxRow][k])) maxRow = i;
      }
      [A[k], A[maxRow]] = [A[maxRow], A[k]];
      [B[k], B[maxRow]] = [B[maxRow], B[k]];
      for (let i = k + 1; i < m; i++) {
        const f = A[i][k] / A[k][k];
        for (let j = k; j < m; j++) {
          A[i][j] -= f * A[k][j];
        }
        B[i] -= f * B[k];
      }
    }
    const x = Array(m).fill(0);
    for (let i = m - 1; i >= 0; i--) {
      x[i] = B[i];
      for (let j = i + 1; j < m; j++) {
        x[i] -= A[i][j] * x[j];
      }
      x[i] /= A[i][i];
    }
    return x;
  }
  const [_c, b, a] = solve(A, B);
  if (a === 0) return null;

  // Find the optimal arousal point (vertex of parabola)
  let optimalArousal = -b / (2 * a);

  // Calculate range using 0.25 standard deviations
  const rangeOffset = 0.25 * arousalStdDev;
  let lowerBound = optimalArousal - rangeOffset;
  let upperBound = optimalArousal + rangeOffset;

  // Clamp to [0, 10] range
  lowerBound = Math.max(0, Math.min(10, lowerBound));
  upperBound = Math.max(0, Math.min(10, upperBound));
  optimalArousal = Math.max(0, Math.min(10, optimalArousal));

  return {
    optimal: optimalArousal,
    lowerBound,
    upperBound,
    range: `${lowerBound.toFixed(1)}-${upperBound.toFixed(1)}`,
    display: `${optimalArousal.toFixed(2)} / ${lowerBound.toFixed(
      1
    )}-${upperBound.toFixed(1)}`,
  };
};
