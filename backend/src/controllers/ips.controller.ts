import { Re<PERSON><PERSON><PERSON><PERSON> } from "express";
import { IPSService } from "../services/ips.service";

export class IPSController {
  /**
   * Create a new IPS record
   */
  static create: RequestHandler = async (req, res) => {
    try {
      const {
        dateTime,
        competitionDateTime,
        competitionName,
        performanceScore,
        arousalScore,
        concentration,
        confidence,
      } = req.body;

      const userId = req.user?.userId;

      if (!userId) {
        res.status(403).json({ message: "Authentication required" });
        return;
      }

      // Validation
      if (!dateTime || !competitionDateTime || !competitionName) {
        res.status(400).json({
          message:
            "dateTime, competitionDateTime, and competitionName are required",
        });
        return;
      }

      if (
        performanceScore === undefined ||
        arousalScore === undefined ||
        typeof performanceScore !== "number" ||
        typeof arousalScore !== "number"
      ) {
        res.status(400).json({
          message: "performanceScore and arousalScore must be numbers",
        });
        return;
      }

      if (performanceScore < 0 || performanceScore > 10) {
        res.status(400).json({
          message: "performanceScore must be between 0 and 10",
        });
        return;
      }

      if (arousalScore < 0 || arousalScore > 10) {
        res.status(400).json({
          message: "arousalScore must be between 0 and 10",
        });
        return;
      }

      const ipsRecord = await IPSService.createIPSRecord({
        coacheeId: userId,
        dateTime: new Date(dateTime),
        competitionDateTime: new Date(competitionDateTime),
        competitionName,
        performanceScore,
        arousalScore,
        concentration,
        confidence,
      });

      res.status(201).json(ipsRecord);
    } catch (error) {
      console.error("Error creating IPS record:", error);
      if (error instanceof Error) {
        res.status(400).json({ message: error.message });
      } else {
        res.status(500).json({ message: "Failed to create IPS record" });
      }
    }
  };

  /**
   * Get all IPS records (with optional filtering)
   */
  static getAll: RequestHandler = async (req, res) => {
    try {
      const userId = req.user?.userId;
      const userRole = req.user?.role;

      if (!userId) {
        res.status(403).json({ message: "Authentication required" });
        return;
      }

      const {
        coacheeId,
        startDate,
        endDate,
        limit = "50",
        offset = "0",
      } = req.query;

      // Parse query parameters
      const parsedLimit = parseInt(limit as string, 10);
      const parsedOffset = parseInt(offset as string, 10);

      // For coachees, only allow them to see their own records
      let targetCoacheeId = coacheeId as string;
      if (userRole === "COACHEE") {
        targetCoacheeId = userId;
      }

      const options = {
        coacheeId: targetCoacheeId,
        startDate: startDate ? new Date(startDate as string) : undefined,
        endDate: endDate ? new Date(endDate as string) : undefined,
        limit: parsedLimit,
        offset: parsedOffset,
      };

      const result = await IPSService.getIPSRecords(options);
      res.status(200).json(result);
    } catch (error) {
      console.error("Error fetching IPS records:", error);
      res.status(500).json({ message: "Failed to fetch IPS records" });
    }
  };

  /**
   * Get IPS record by ID
   */
  static getById: RequestHandler = async (req, res) => {
    try {
      const { id } = req.params;
      const userId = req.user?.userId;
      const userRole = req.user?.role;

      if (!userId) {
        res.status(403).json({ message: "Authentication required" });
        return;
      }

      const ipsRecord = await IPSService.getIPSRecordById(id);

      // Check permissions - coachees can only view their own records
      if (userRole === "COACHEE" && ipsRecord.coacheeId !== userId) {
        res.status(403).json({ message: "Access denied" });
        return;
      }

      res.status(200).json(ipsRecord);
    } catch (error) {
      console.error("Error fetching IPS record:", error);
      if (error instanceof Error && error.message === "IPS record not found") {
        res.status(404).json({ message: "IPS record not found" });
      } else {
        res.status(500).json({ message: "Failed to fetch IPS record" });
      }
    }
  };

  /**
   * Update IPS record
   */
  static update: RequestHandler = async (req, res) => {
    try {
      const { id } = req.params;
      const userId = req.user?.userId;
      const userRole = req.user?.role;

      if (!userId) {
        res.status(403).json({ message: "Authentication required" });
        return;
      }

      // First, get the existing record to check permissions
      const existingRecord = await IPSService.getIPSRecordById(id);

      // Check permissions - coachees can only update their own records
      if (userRole === "COACHEE" && existingRecord.coacheeId !== userId) {
        res.status(403).json({ message: "Access denied" });
        return;
      }

      const {
        dateTime,
        competitionDateTime,
        competitionName,
        performanceScore,
        arousalScore,
        concentration,
        confidence,
      } = req.body;

      const updateData: any = {};

      if (dateTime) updateData.dateTime = new Date(dateTime);
      if (competitionDateTime)
        updateData.competitionDateTime = new Date(competitionDateTime);
      if (competitionName) updateData.competitionName = competitionName;
      if (performanceScore !== undefined)
        updateData.performanceScore = performanceScore;
      if (arousalScore !== undefined) updateData.arousalScore = arousalScore;
      if (concentration !== undefined) updateData.concentration = concentration;
      if (confidence !== undefined) updateData.confidence = confidence;

      const updatedRecord = await IPSService.updateIPSRecord(id, updateData);
      res.status(200).json(updatedRecord);
    } catch (error) {
      console.error("Error updating IPS record:", error);
      if (error instanceof Error && error.message === "IPS record not found") {
        res.status(404).json({ message: "IPS record not found" });
      } else if (error instanceof Error) {
        res.status(400).json({ message: error.message });
      } else {
        res.status(500).json({ message: "Failed to update IPS record" });
      }
    }
  };

  /**
   * Delete IPS record
   */
  static delete: RequestHandler = async (req, res) => {
    try {
      const { id } = req.params;
      const userId = req.user?.userId;
      const userRole = req.user?.role;

      if (!userId) {
        res.status(403).json({ message: "Authentication required" });
        return;
      }

      // First, get the existing record to check permissions
      const existingRecord = await IPSService.getIPSRecordById(id);

      // Check permissions - coachees can only delete their own records
      if (userRole === "COACHEE" && existingRecord.coacheeId !== userId) {
        res.status(403).json({ message: "Access denied" });
        return;
      }

      await IPSService.deleteIPSRecord(id);
      res.status(200).json({ message: "IPS record deleted successfully" });
    } catch (error) {
      console.error("Error deleting IPS record:", error);
      if (error instanceof Error && error.message === "IPS record not found") {
        res.status(404).json({ message: "IPS record not found" });
      } else {
        res.status(500).json({ message: "Failed to delete IPS record" });
      }
    }
  };

  /**
   * Get IPS trends for visualization
   */
  static getTrends: RequestHandler = async (req, res) => {
    try {
      const userId = req.user?.userId;
      const userRole = req.user?.role;

      if (!userId) {
        res.status(403).json({ message: "Authentication required" });
        return;
      }

      const { coacheeId, startDate, endDate } = req.query;

      // For coachees, only allow them to see their own trends
      let targetCoacheeId = coacheeId as string;
      if (userRole === "COACHEE") {
        targetCoacheeId = userId;
      }

      if (!targetCoacheeId) {
        res.status(400).json({ message: "coacheeId is required" });
        return;
      }

      const trends = await IPSService.getIPSTrends(
        targetCoacheeId,
        startDate ? new Date(startDate as string) : undefined,
        endDate ? new Date(endDate as string) : undefined
      );

      res.status(200).json(trends);
    } catch (error) {
      console.error("Error fetching IPS trends:", error);
      res.status(500).json({ message: "Failed to fetch IPS trends" });
    }
  };
}

export const ipsController = IPSController;
