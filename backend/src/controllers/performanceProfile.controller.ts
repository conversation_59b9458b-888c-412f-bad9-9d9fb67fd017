import { Request, Response, RequestHandler } from "express";
import { PerformanceProfileService } from "../services/performanceProfile.service";
import { PerformanceCategory } from "@prisma/client";

export class PerformanceProfileController {
  static createPerformanceProfile: RequestHandler = async (
    req: Request,
    res: Response
  ) => {
    try {
      const userId = req.user?.userId;
      if (!userId) {
        res.status(401).json({ error: "Unauthorized" });
        return;
      }

      const { startDate, targetDate, goals } = req.body;

      // Validate required fields
      if (!startDate || !targetDate || !goals || !Array.isArray(goals)) {
        res.status(400).json({
          error:
            "Missing required fields: startDate, targetDate, and goals array",
        });
        return;
      }

      // Validate date format and logic
      const start = new Date(startDate);
      const target = new Date(targetDate);

      if (isNaN(start.getTime()) || isNaN(target.getTime())) {
        res.status(400).json({ error: "Invalid date format" });
        return;
      }

      if (target <= start) {
        res.status(400).json({ error: "Target date must be after start date" });
        return;
      }

      // Validate goals
      if (goals.length === 0) {
        res.status(400).json({ error: "At least one goal is required" });
        return;
      }

      for (const goal of goals) {
        if (
          !goal.category ||
          !goal.goalName ||
          typeof goal.currentRating !== "number" ||
          typeof goal.targetRating !== "number"
        ) {
          res.status(400).json({
            error:
              "Each goal must have category, goalName, currentRating, and targetRating",
          });
          return;
        }

        if (!Object.values(PerformanceCategory).includes(goal.category)) {
          res.status(400).json({
            error: `Invalid category: ${
              goal.category
            }. Must be one of: ${Object.values(PerformanceCategory).join(
              ", "
            )}`,
          });
          return;
        }
      }

      const performanceProfile =
        await PerformanceProfileService.createPerformanceProfile({
          coacheeId: userId,
          startDate: start,
          targetDate: target,
          goals,
        });

      res.status(201).json(performanceProfile);
    } catch (error: any) {
      console.error("Error creating performance profile:", error);
      res.status(500).json({ error: error.message || "Internal server error" });
    }
  };

  static getPerformanceProfiles: RequestHandler = async (
    req: Request,
    res: Response
  ) => {
    try {
      const userId = req.user?.userId;
      const userRole = req.user?.role;

      if (!userId) {
        res.status(401).json({ error: "Unauthorized" });
        return;
      }

      // For coachees, only show their own profiles
      // For coaches, allow querying specific coachee profiles via query param
      let coacheeId = userId;

      if (userRole === "COACH" && req.query.coacheeId) {
        coacheeId = req.query.coacheeId as string;
        // TODO: Add validation that coach has access to this coachee
      }

      const limit = req.query.limit ? parseInt(req.query.limit as string) : 10;
      const offset = req.query.offset
        ? parseInt(req.query.offset as string)
        : 0;

      const profiles = await PerformanceProfileService.getPerformanceProfiles({
        coacheeId,
        limit,
        offset,
      });

      res.json(profiles);
    } catch (error: any) {
      console.error("Error fetching performance profiles:", error);
      res.status(500).json({ error: error.message || "Internal server error" });
    }
  };

  static getPerformanceProfileById: RequestHandler = async (
    req: Request,
    res: Response
  ) => {
    try {
      const userId = req.user?.userId;
      const userRole = req.user?.role;
      const profileId = req.params.id;

      if (!userId) {
        res.status(401).json({ error: "Unauthorized" });
        return;
      }

      const profile = await PerformanceProfileService.getPerformanceProfileById(
        profileId
      );

      // Check permissions: coachees can only view their own profiles
      if (userRole === "COACHEE" && profile.coacheeId !== userId) {
        res.status(403).json({ error: "Access denied" });
        return;
      }

      // TODO: For coaches, validate they have access to this coachee

      res.json(profile);
    } catch (error: any) {
      console.error("Error fetching performance profile:", error);
      if (error.message === "Performance profile not found") {
        res.status(404).json({ error: error.message });
      } else {
        res
          .status(500)
          .json({ error: error.message || "Internal server error" });
      }
    }
  };

  static updatePerformanceGoals: RequestHandler = async (
    req: Request,
    res: Response
  ) => {
    try {
      const userId = req.user?.userId;
      const userRole = req.user?.role;
      const profileId = req.params.id;
      const { goals } = req.body;

      if (!userId) {
        res.status(401).json({ error: "Unauthorized" });
        return;
      }

      if (!goals || !Array.isArray(goals)) {
        res.status(400).json({ error: "Goals array is required" });
        return;
      }

      // Validate goal updates
      for (const goal of goals) {
        // For new goals (without id), validate required fields
        if (!goal.id) {
          if (
            !goal.category ||
            !goal.goalName ||
            typeof goal.currentRating !== "number" ||
            typeof goal.targetRating !== "number"
          ) {
            res.status(400).json({
              error:
                "New goals must have category, goalName, currentRating, and targetRating",
            });
            return;
          }

          if (!Object.values(PerformanceCategory).includes(goal.category)) {
            res.status(400).json({
              error: `Invalid category: ${
                goal.category
              }. Must be one of: ${Object.values(PerformanceCategory).join(
                ", "
              )}`,
            });
            return;
          }
        }

        // Validate rating ranges for both new and existing goals
        if (
          goal.currentRating !== undefined &&
          (typeof goal.currentRating !== "number" ||
            goal.currentRating < 0 ||
            goal.currentRating > 10)
        ) {
          res.status(400).json({
            error: "Current rating must be a number between 0 and 10",
          });
          return;
        }

        if (
          goal.targetRating !== undefined &&
          (typeof goal.targetRating !== "number" ||
            goal.targetRating < 0 ||
            goal.targetRating > 10)
        ) {
          res
            .status(400)
            .json({ error: "Target rating must be a number between 0 and 10" });
          return;
        }
      }

      // Get profile to check permissions
      const profile = await PerformanceProfileService.getPerformanceProfileById(
        profileId
      );

      // Check permissions: coachees can only update their own profiles
      if (userRole === "COACHEE" && profile.coacheeId !== userId) {
        res.status(403).json({ error: "Access denied" });
        return;
      }

      // TODO: For coaches, validate they have access to this coachee

      const updatedProfile =
        await PerformanceProfileService.updatePerformanceGoals(
          profileId,
          goals
        );

      res.json(updatedProfile);
    } catch (error: any) {
      console.error("Error updating performance goals:", error);
      if (error.message === "Performance profile not found") {
        res.status(404).json({ error: error.message });
      } else {
        res
          .status(500)
          .json({ error: error.message || "Internal server error" });
      }
    }
  };

  static getLatestActiveGoals: RequestHandler = async (
    req: Request,
    res: Response
  ) => {
    try {
      const userId = req.user?.userId;
      const userRole = req.user?.role;

      if (!userId) {
        res.status(401).json({ error: "Unauthorized" });
        return;
      }

      // For coachees, get their own goals
      // For coaches, allow querying specific coachee goals via query param
      let coacheeId = userId;

      if (userRole === "COACH" && req.query.coacheeId) {
        coacheeId = req.query.coacheeId as string;
        // TODO: Add validation that coach has access to this coachee
      }

      const latestGoals =
        await PerformanceProfileService.getLatestActiveGoalsByCategory(
          coacheeId
        );

      if (!latestGoals) {
        res.json({ message: "No performance profiles found" });
        return;
      }

      res.json(latestGoals);
    } catch (error: any) {
      console.error("Error fetching latest active goals:", error);
      res.status(500).json({ error: error.message || "Internal server error" });
    }
  };

  static deletePerformanceProfile: RequestHandler = async (
    req: Request,
    res: Response
  ) => {
    try {
      const userId = req.user?.userId;
      const userRole = req.user?.role;
      const profileId = req.params.id;

      if (!userId) {
        res.status(401).json({ error: "Unauthorized" });
        return;
      }

      // Get profile to check permissions
      const profile = await PerformanceProfileService.getPerformanceProfileById(
        profileId
      );

      // Check permissions: coachees can only delete their own profiles
      if (userRole === "COACHEE" && profile.coacheeId !== userId) {
        res.status(403).json({ error: "Access denied" });
        return;
      }

      // TODO: For coaches, validate they have access to this coachee

      await PerformanceProfileService.deletePerformanceProfile(profileId);

      res.json({ message: "Performance profile deleted successfully" });
    } catch (error: any) {
      console.error("Error deleting performance profile:", error);
      if (error.message === "Performance profile not found") {
        res.status(404).json({ error: error.message });
      } else {
        res
          .status(500)
          .json({ error: error.message || "Internal server error" });
      }
    }
  };
}

export const performanceProfileController = PerformanceProfileController;
