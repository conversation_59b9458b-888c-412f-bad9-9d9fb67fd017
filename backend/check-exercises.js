const { PrismaClient } = require('@prisma/client');

const prisma = new PrismaClient();

async function checkExercises() {
  try {
    const exercises = await prisma.exercise.findMany({
      select: {
        id: true,
        name: true,
        createdAt: true
      }
    });
    
    console.log('Current exercises in database:');
    console.log(JSON.stringify(exercises, null, 2));
    
    // Check for exercises with invalid IDs (not UUID format)
    const invalidIdExercises = exercises.filter(exercise => {
      const uuidRegex = /^[0-9a-f]{8}-[0-9a-f]{4}-[1-5][0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}$/i;
      return !uuidRegex.test(exercise.id);
    });
    
    console.log('\nExercises with invalid IDs:');
    console.log(JSON.stringify(invalidIdExercises, null, 2));
    
  } catch (error) {
    console.error('Error checking exercises:', error);
  } finally {
    await prisma.$disconnect();
  }
}

checkExercises();
